// .eslintrc.cjs
/* eslint-env node */

const path = require('node:path');

/** @type {import('eslint').Linter.Config} */
module.exports = {
  root: true,                       // stop ESLint from looking above repo root
  parser: '@typescript-eslint/parser',
  parserOptions: {
    tsconfigRootDir: __dirname, // monorepo-friendly
    project: ['./tsconfig.base.json'],        // point to base tsconfig
    sourceType: 'module',
  },

  plugins: [
    '@typescript-eslint',
    'import',
    'unused-imports',
    'prettier',
  ],

  extends: [
    'eslint:recommended',
    'plugin:@typescript-eslint/recommended-type-checked',  // stricter TS rules
    'plugin:import/recommended',
    'plugin:import/typescript',
    'plugin:prettier/recommended',                         // keeps Prettier last
  ],

  settings: {
    'import/resolver': {
      typescript: {
        alwaysTryTypes: true,
        project: ['./tsconfig.base.json'],
      },
    },
  },

  rules: {
    /* ---- stylistic handled by Prettier ---- */
    'prettier/prettier'            : [
      'error',
      {
        singleQuote   : true,
        trailingComma : 'es5',
        tabWidth      : 2,
        printWidth    : 100,
        arrowParens   : 'avoid',
      },
    ],

    /* ---- import / ESM hygiene ---- */
    'import/extensions'            : 'off', // TypeScript handles this with moduleResolution: NodeNext
    'import/no-unresolved'         : 'error',
    'import/order'                 : ['warn', { 'newlines-between': 'always' }],

    /* ---- kill dead code early ---- */
    'unused-imports/no-unused-imports' : 'warn',
    'unused-imports/no-unused-vars'    : [
      'warn',
      { vars: 'all', varsIgnorePattern: '^_', args: 'after-used', argsIgnorePattern: '^_' },
    ],
  },

  overrides: [
    /* Node-only (no browser globals) */
    {
      files: ['**/*.cjs', '**/*.mjs', '**/scripts/**/*.{js,ts}'],
      env  : { node: true },
    },

    /* Test files can use dev-deps */
    {
      files: ['**/*.test.{ts,js}'],
      env  : { jest: true, node: true },
      rules: { 'import/no-extraneous-dependencies': 'off' },
    },
  ],

  ignorePatterns: [
    'dist', 'build', '.turbo', '.next', 'coverage', '**/dist/**', '**/node_modules/**'
  ],
};