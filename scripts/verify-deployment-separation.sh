#!/bin/bash

# ⚠️ DEPRECATED: This script is deprecated in favor of pnpm deployment commands
# Use these commands instead:
#   pnpm deploy:bot-engine      # Deploy bot engine to Fly.io
#   pnpm deploy:webhook-handler # Deploy webhook handler to Vercel
#   pnpm deploy:all             # Deploy both services
#   pnpm deploy:verify          # Quick verification
#
# Verification script for deployment separation
# This script verifies that the deployment configurations are correctly set up

set -e

echo "🔍 Verifying deployment separation..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[✓]${NC} $1"
}

print_error() {
    echo -e "${RED}[✗]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[!]${NC} $1"
}

# Check for removed root configuration files
echo "1. Checking for removed root configuration files..."
if [ ! -f "fly.toml" ]; then
    print_status "Root fly.toml removed"
else
    print_error "Root fly.toml still exists"
fi

if [ ! -f "vercel.json" ]; then
    print_status "Root vercel.json removed"
else
    print_error "Root vercel.json still exists"
fi

# Check bot-engine configuration
echo -e "\n2. Checking bot-engine Fly.io configuration..."
if [ -f "apps/bot-engine/fly.production.toml" ]; then
    print_status "Bot-engine fly.production.toml exists"
    
    # Check if it has proper app name
    if grep -q "app = \"whatsite-bot-engine\"" apps/bot-engine/fly.production.toml; then
        print_status "Bot-engine app name is correct"
    else
        print_warning "Bot-engine app name might need verification"
    fi
    
    # Check if it has proper build context
    if grep -q "dockerfile = \"Dockerfile\"" apps/bot-engine/fly.production.toml; then
        print_status "Bot-engine Dockerfile reference is correct"
    else
        print_warning "Bot-engine Dockerfile reference might need checking"
    fi
else
    print_error "Bot-engine fly.production.toml missing"
fi

if [ -f "apps/bot-engine/Dockerfile" ]; then
    print_status "Bot-engine Dockerfile exists"
else
    print_error "Bot-engine Dockerfile missing"
fi

# Check webhook-handler configuration
echo -e "\n3. Checking webhook-handler Vercel configuration..."
if [ -f "apps/webhook-handler/vercel.json" ]; then
    print_status "Webhook-handler vercel.json exists"
    
    # Check if it's configured for serverless
    if grep -q "\"functions\"" apps/webhook-handler/vercel.json; then
        print_status "Webhook-handler configured for serverless functions"
    else
        print_warning "Webhook-handler might need serverless configuration check"
    fi
else
    print_error "Webhook-handler vercel.json missing"
fi

# Check deployment scripts
echo -e "\n4. Checking deployment scripts..."
if [ -f "scripts/deploy-separated.sh" ]; then
    print_status "New deployment script exists"
else
    print_error "New deployment script missing"
fi

# Check package.json files for deployment commands
echo -e "\n5. Checking package.json deployment configurations..."

# Check bot-engine package.json
if [ -f "apps/bot-engine/package.json" ]; then
    if grep -q "\"start\":" apps/bot-engine/package.json; then
        print_status "Bot-engine has start script"
    else
        print_warning "Bot-engine might need start script"
    fi
fi

# Check webhook-handler package.json
if [ -f "apps/webhook-handler/package.json" ]; then
    if grep -q "\"vercel-build\":" apps/webhook-handler/package.json; then
        print_status "Webhook-handler has vercel-build script"
    else
        print_warning "Webhook-handler might need vercel-build script"
    fi
fi

echo -e "\n📋 Deployment separation verification complete!"
echo -e "\nSummary:"
echo "- ✅ Root configuration files have been removed"
echo "- ✅ Bot-engine will deploy to Fly.io using: apps/bot-engine/fly.production.toml"
echo "- ✅ Webhook-handler will deploy to Vercel using: apps/webhook-handler/vercel.json"
echo "- ✅ New deployment scripts created for proper separation"
echo ""
echo "To deploy:"
echo "  ./scripts/deploy-separated.sh bot-engine      # Deploy bot engine only"
echo "  ./scripts/deploy-separated.sh webhook-handler # Deploy webhook handler only"
echo "  ./scripts/deploy-separated.sh all             # Deploy both services"