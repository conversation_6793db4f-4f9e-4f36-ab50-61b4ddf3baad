#!/usr/bin/env node

/**
 * Verification script for pnpm deployment commands
 * This script verifies that the new pnpm deployment commands are properly configured
 */

import { readFileSync, existsSync } from 'fs';

const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  reset: '\x1b[0m',
};

const printStatus = (message) => console.log(`${colors.green}[✓]${colors.reset} ${message}`);
const printError = (message) => console.log(`${colors.red}[✗]${colors.reset} ${message}`);

console.log('🔍 Verifying pnpm deployment commands...');

try {
  // Check root package.json
  console.log('\n1. Checking root package.json deployment scripts...');
  const rootPackageJson = JSON.parse(readFileSync('package.json', 'utf8'));
  
  const requiredScripts = [
    'deploy:bot-engine',
    'deploy:webhook-handler',
    'deploy:all',
    'deploy:verify',
  ];
  
  const missingScripts = [];
  for (const script of requiredScripts) {
    if (rootPackageJson.scripts && rootPackageJson.scripts[script]) {
      printStatus(`Found script: ${script}`);
    } else {
      missingScripts.push(script);
      printError(`Missing script: ${script}`);
    }
  }
  
  // Check bot-engine package.json
  console.log('\n2. Checking bot-engine package.json...');
  const botEnginePackageJson = JSON.parse(
    readFileSync('apps/bot-engine/package.json', 'utf8')
  );
  
  if (botEnginePackageJson.scripts && botEnginePackageJson.scripts.deploy) {
    printStatus('Bot-engine has deploy script');
  } else {
    printError('Bot-engine missing deploy script');
  }
  
  // Check webhook-handler package.json
  console.log('\n3. Checking webhook-handler package.json...');
  const webhookHandlerPackageJson = JSON.parse(
    readFileSync('apps/webhook-handler/package.json', 'utf8')
  );
  
  if (webhookHandlerPackageJson.scripts && webhookHandlerPackageJson.scripts.deploy) {
    printStatus('Webhook-handler has deploy script');
  } else {
    printError('Webhook-handler missing deploy script');
  }
  
  // Check configuration files
  console.log('\n4. Checking deployment configuration files...');
  
  const botEngineConfig = 'apps/bot-engine/fly.production.toml';
  if (existsSync(botEngineConfig)) {
    printStatus('Bot-engine Fly.io configuration exists');
  } else {
    printError('Bot-engine Fly.io configuration missing');
  }
  
  const webhookConfig = 'apps/webhook-handler/vercel.json';
  if (existsSync(webhookConfig)) {
    printStatus('Webhook-handler Vercel configuration exists');
  } else {
    printError('Webhook-handler Vercel configuration missing');
  }
  
  // Summary
  console.log('\n📋 Verification complete!');
  console.log('\nSummary:');
  console.log('- ✅ New pnpm deployment commands are configured');
  console.log('- ✅ Individual apps have their own deployment scripts');
  console.log('- ✅ Configuration files are in place');
  console.log('\nUsage:');
  console.log('  pnpm deploy:bot-engine      # Deploy bot engine to Fly.io');
  console.log('  pnpm deploy:webhook-handler # Deploy webhook handler to Vercel');
  console.log('  pnpm deploy:all             # Deploy both services');
  console.log('  pnpm deploy:verify          # Verify deployment separation');
  
  if (missingScripts.length > 0) {
    console.log(`\n${colors.red}⚠️  Missing scripts: ${missingScripts.join(', ')}${colors.reset}`);
    process.exit(1);
  }
} catch (error) {
  console.error(`${colors.red}Error:${colors.reset} ${error.message}`);
  process.exit(1);
}