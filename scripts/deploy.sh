#!/bin/bash

# ⚠️ DEPRECATED: This script is deprecated in favor of pnpm deployment commands
# Use these commands instead:
#   pnpm deploy:bot-engine      # Deploy bot engine to Fly.io
#   pnpm deploy:webhook-handler # Deploy webhook handler to Vercel
#   pnpm deploy:all             # Deploy both services
#
# Deployment Script for Whatsite Bot - Handles both Fly.io and Vercel
# Comprehensive deployment with validation, monitoring, and rollback capabilities

set -e

# Configuration
BOT_ENGINE_APP_NAME="whatsite-bot-engine"
BOT_ENGINE_REGION="sjc"
BOT_ENGINE_DOCKERFILE_PATH="apps/bot-engine/Dockerfile"
BOT_ENGINE_FLY_TOML_PATH="apps/bot-engine/fly.production.toml"
BOT_ENGINE_HEALTH_CHECK_URL="https://${BOT_ENGINE_APP_NAME}.fly.dev/health"
BOT_ENGINE_READY_CHECK_URL="https://${BOT_ENGINE_APP_NAME}.fly.dev/ready"
BOT_ENGINE_METRICS_URL="https://${BOT_ENGINE_APP_NAME}.fly.dev/metrics"

WEBHOOK_HANDLER_PATH="apps/webhook-handler"
WEBHOOK_HANDLER_VERCEL_JSON="apps/webhook-handler/vercel.json"

MAX_DEPLOY_TIME=600  # 10 minutes
ROLLBACK_ENABLED=true

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    if ! command_exists fly; then
        log_error "Fly CLI is not installed. Please install it first."
        exit 1
    fi
    
    if ! command_exists docker; then
        log_error "Docker is not installed. Please install it first."
        exit 1
    fi
    
    if ! command_exists jq; then
        log_error "jq is not installed. Please install it first."
        exit 1
    fi
    
    if ! command_exists pnpm; then
        log_error "pnpm is not installed. Please install it first."
        exit 1
    fi
    
    # Check if we're logged into Fly.io
    if ! fly auth whoami >/dev/null 2>&1; then
        log_error "Not logged into Fly.io. Please run 'fly auth login' first."
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

# Validate environment variables
validate_environment() {
    log_info "Validating environment variables..."
    
    local required_vars=(
        "TWILIO_ACCOUNT_SID"
        "TWILIO_AUTH_TOKEN"
        "TWILIO_PHONE_NUMBER"
        "GOOGLE_API_KEY"
        "GITHUB_TOKEN"
        "VERCEL_TOKEN"
    )
    
    local missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var}" ]]; then
            missing_vars+=("$var")
        fi
    done
    
    if [[ ${#missing_vars[@]} -gt 0 ]]; then
        log_error "Missing required environment variables:"
        for var in "${missing_vars[@]}"; do
            log_error "  - $var"
        done
        exit 1
    fi
    
    log_success "Environment variables validation passed"
}

# Set Fly.io secrets
set_secrets() {
    log_info "Setting Fly.io secrets..."
    
    local secrets=(
        "TWILIO_ACCOUNT_SID=${TWILIO_ACCOUNT_SID}"
        "TWILIO_AUTH_TOKEN=${TWILIO_AUTH_TOKEN}"
        "TWILIO_PHONE_NUMBER=${TWILIO_PHONE_NUMBER}"
        "TWILIO_WEBHOOK_URL=https://${APP_NAME}.fly.dev/webhook"
        "GOOGLE_API_KEY=${GOOGLE_API_KEY}"
        "GITHUB_TOKEN=${GITHUB_TOKEN}"
        "VERCEL_TOKEN=${VERCEL_TOKEN}"
    )
    
    # Add optional secrets if they exist
    [[ -n "${VERCEL_TEAM_ID}" ]] && secrets+=("VERCEL_TEAM_ID=${VERCEL_TEAM_ID}")
    [[ -n "${GITHUB_ORGANIZATION}" ]] && secrets+=("GITHUB_ORGANIZATION=${GITHUB_ORGANIZATION}")
    [[ -n "${WEBHOOK_SECRET_KEY}" ]] && secrets+=("WEBHOOK_SECRET_KEY=${WEBHOOK_SECRET_KEY}")
    
    for secret in "${secrets[@]}"; do
        echo "$secret" | fly secrets set --stdin
    done
    
    log_success "Secrets set successfully"
}

# Build and test locally
build_and_test() {
    log_info "Building and testing locally..."
    
    # Install dependencies
    log_info "Installing dependencies..."
    pnpm install --frozen-lockfile
    
    # Build the application
    log_info "Building application..."
    pnpm --filter bot-engine build
    
    # Run tests
    log_info "Running tests..."
    pnpm --filter bot-engine test || log_warning "Tests failed or not configured"
    
    # Type check
    log_info "Type checking..."
    pnpm --filter bot-engine typecheck
    
    log_success "Build and test completed"
}

# Create volumes if they don't exist
create_volumes() {
    log_info "Creating volumes..."
    
    local volumes=(
        "whatsite_workspaces:50gb"
        "whatsite_sessions:20gb"
    )
    
    for volume_spec in "${volumes[@]}"; do
        local volume_name=$(echo "$volume_spec" | cut -d':' -f1)
        local volume_size=$(echo "$volume_spec" | cut -d':' -f2)
        
        if ! fly volumes list | grep -q "$volume_name"; then
            log_info "Creating volume: $volume_name ($volume_size)"
            fly volumes create "$volume_name" --size "$volume_size" --region "$REGION"
        else
            log_info "Volume $volume_name already exists"
        fi
    done
    
    log_success "Volumes created/verified"
}

# Deploy to staging first
deploy_staging() {
    log_info "Deploying to staging environment..."
    
    # Use staging configuration
    local staging_config=$(mktemp)
    cp "$FLY_TOML_PATH" "$staging_config"
    
    # Modify for staging
    sed -i 's/app = "whatsite-bot-engine"/app = "whatsite-bot-engine-staging"/' "$staging_config"
    sed -i 's/min_machines_running = 1/min_machines_running = 0/' "$staging_config"
    sed -i 's/max_machines_running = 5/max_machines_running = 1/' "$staging_config"
    
    # Deploy staging
    fly deploy --config "$staging_config" --build-arg NODE_ENV=staging
    
    # Wait for staging deployment
    local staging_url="https://whatsite-bot-engine-staging.fly.dev"
    wait_for_health_check "$staging_url/health" 300
    
    # Run smoke tests on staging
    run_smoke_tests "$staging_url"
    
    log_success "Staging deployment completed"
    
    # Cleanup
    rm "$staging_config"
}

# Deploy to production
deploy_production() {
    log_info "Deploying to production..."
    
    # Use production configuration
    local config_file="$FLY_TOML_PATH"
    if [[ -f "$PRODUCTION_TOML_PATH" ]]; then
        config_file="$PRODUCTION_TOML_PATH"
    fi
    
    # Get current deployment info for rollback
    local current_version=""
    if fly status >/dev/null 2>&1; then
        current_version=$(fly status --json | jq -r '.Deployment.Id // ""')
    fi
    
    # Deploy with rolling strategy
    fly deploy --config "$config_file" --strategy rolling --build-arg NODE_ENV=production
    
    # Wait for deployment to complete
    wait_for_deployment "$MAX_DEPLOY_TIME"
    
    # Wait for health checks
    wait_for_health_check "$HEALTH_CHECK_URL" 300
    wait_for_health_check "$READY_CHECK_URL" 180
    
    # Run comprehensive tests
    run_comprehensive_tests
    
    # Monitor deployment for initial period
    monitor_deployment 300  # 5 minutes
    
    log_success "Production deployment completed successfully"
    
    # Store deployment info
    echo "$current_version" > .last_known_good_deployment
}

# Wait for deployment to complete
wait_for_deployment() {
    local timeout=$1
    local start_time=$(date +%s)
    
    log_info "Waiting for deployment to complete (timeout: ${timeout}s)..."
    
    while true; do
        local current_time=$(date +%s)
        local elapsed=$((current_time - start_time))
        
        if [[ $elapsed -gt $timeout ]]; then
            log_error "Deployment timed out after ${timeout} seconds"
            return 1
        fi
        
        local status=$(fly status --json | jq -r '.Deployment.Status // "unknown"')
        
        case "$status" in
            "successful")
                log_success "Deployment completed successfully"
                return 0
                ;;
            "failed")
                log_error "Deployment failed"
                return 1
                ;;
            *)
                log_info "Deployment in progress... (${elapsed}s elapsed)"
                sleep 10
                ;;
        esac
    done
}

# Wait for health check to pass
wait_for_health_check() {
    local url=$1
    local timeout=$2
    local start_time=$(date +%s)
    
    log_info "Waiting for health check: $url (timeout: ${timeout}s)..."
    
    while true; do
        local current_time=$(date +%s)
        local elapsed=$((current_time - start_time))
        
        if [[ $elapsed -gt $timeout ]]; then
            log_error "Health check timed out after ${timeout} seconds"
            return 1
        fi
        
        if curl -s -f "$url" >/dev/null 2>&1; then
            log_success "Health check passed"
            return 0
        fi
        
        log_info "Health check in progress... (${elapsed}s elapsed)"
        sleep 5
    done
}

# Run smoke tests
run_smoke_tests() {
    local base_url=$1
    
    log_info "Running smoke tests on $base_url..."
    
    local endpoints=(
        "/health"
        "/ready"
        "/metrics"
    )
    
    for endpoint in "${endpoints[@]}"; do
        local url="${base_url}${endpoint}"
        log_info "Testing endpoint: $url"
        
        local response=$(curl -s -w "%{http_code}" -o /dev/null "$url")
        
        if [[ "$response" == "200" ]]; then
            log_success "✓ $endpoint - OK"
        else
            log_error "✗ $endpoint - Failed (HTTP $response)"
            return 1
        fi
    done
    
    log_success "All smoke tests passed"
}

# Run comprehensive tests
run_comprehensive_tests() {
    log_info "Running comprehensive tests..."
    
    # Test health endpoints
    run_smoke_tests "https://${APP_NAME}.fly.dev"
    
    # Test metrics endpoint
    log_info "Testing metrics endpoint..."
    local metrics_response=$(curl -s "$METRICS_URL")
    if echo "$metrics_response" | grep -q "http_requests_total"; then
        log_success "✓ Metrics endpoint working"
    else
        log_error "✗ Metrics endpoint not working properly"
        return 1
    fi
    
    # Test webhook endpoint (if accessible)
    log_info "Testing webhook endpoint..."
    local webhook_response=$(curl -s -w "%{http_code}" -o /dev/null "https://${APP_NAME}.fly.dev/webhook")
    if [[ "$webhook_response" == "405" ]] || [[ "$webhook_response" == "400" ]]; then
        log_success "✓ Webhook endpoint accessible"
    else
        log_warning "⚠ Webhook endpoint response: $webhook_response"
    fi
    
    log_success "Comprehensive tests completed"
}

# Monitor deployment
monitor_deployment() {
    local duration=$1
    local start_time=$(date +%s)
    
    log_info "Monitoring deployment for ${duration} seconds..."
    
    while true; do
        local current_time=$(date +%s)
        local elapsed=$((current_time - start_time))
        
        if [[ $elapsed -gt $duration ]]; then
            break
        fi
        
        # Check health
        if ! curl -s -f "$HEALTH_CHECK_URL" >/dev/null 2>&1; then
            log_error "Health check failed during monitoring"
            return 1
        fi
        
        # Check metrics for errors
        local metrics=$(curl -s "$METRICS_URL" || echo "")
        if [[ -n "$metrics" ]]; then
            local error_count=$(echo "$metrics" | grep "errors_total" | tail -1 | awk '{print $2}' || echo "0")
            if [[ "$error_count" -gt 10 ]]; then
                log_warning "High error count detected: $error_count"
            fi
        fi
        
        sleep 30
    done
    
    log_success "Monitoring completed - deployment appears stable"
}

# Rollback deployment
rollback_deployment() {
    if [[ "$ROLLBACK_ENABLED" == "true" ]] && [[ -f ".last_known_good_deployment" ]]; then
        local last_good_version=$(cat .last_known_good_deployment)
        
        if [[ -n "$last_good_version" ]]; then
            log_warning "Rolling back to previous deployment: $last_good_version"
            fly releases rollback "$last_good_version"
            
            # Wait for rollback to complete
            wait_for_deployment 300
            wait_for_health_check "$HEALTH_CHECK_URL" 180
            
            log_success "Rollback completed"
        else
            log_error "No previous deployment found for rollback"
        fi
    else
        log_error "Rollback not enabled or no previous deployment found"
    fi
}

# Cleanup function
cleanup() {
    log_info "Cleaning up temporary files..."
    # Add cleanup logic here
}

# Main deployment function
main() {
    local environment=${1:-production}
    local skip_staging=${2:-false}
    
    log_info "Starting deployment to $environment..."
    
    # Set trap for cleanup
    trap cleanup EXIT
    
    # Run deployment steps
    check_prerequisites
    validate_environment
    build_and_test
    create_volumes
    set_secrets
    
    case "$environment" in
        "staging")
            deploy_staging
            ;;
        "production")
            if [[ "$skip_staging" != "true" ]]; then
                deploy_staging
            fi
            if deploy_production; then
                log_success "🎉 Deployment completed successfully!"
            else
                log_error "❌ Deployment failed"
                if [[ "$ROLLBACK_ENABLED" == "true" ]]; then
                    rollback_deployment
                fi
                exit 1
            fi
            ;;
        *)
            log_error "Invalid environment: $environment"
            exit 1
            ;;
    esac
}

# Show usage
show_usage() {
    echo "Usage: $0 [staging|production] [skip_staging]"
    echo ""
    echo "Examples:"
    echo "  $0 staging                    # Deploy to staging only"
    echo "  $0 production                 # Deploy to production (via staging)"
    echo "  $0 production skip_staging    # Deploy directly to production"
    echo ""
    echo "Environment variables required:"
    echo "  TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN, TWILIO_PHONE_NUMBER"
    echo "  GOOGLE_API_KEY, GITHUB_TOKEN, VERCEL_TOKEN"
    echo ""
    echo "Optional environment variables:"
    echo "  VERCEL_TEAM_ID, GITHUB_ORGANIZATION, WEBHOOK_SECRET_KEY"
}

# Handle command line arguments
if [[ $# -eq 0 ]]; then
    show_usage
    exit 1
fi

if [[ "$1" == "--help" ]] || [[ "$1" == "-h" ]]; then
    show_usage
    exit 0
fi

# Run main function
main "$@"