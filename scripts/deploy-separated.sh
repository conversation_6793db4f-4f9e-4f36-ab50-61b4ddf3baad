#!/bin/bash

# ⚠️ DEPRECATED: This script is deprecated in favor of pnpm deployment commands
# Use these commands instead:
#   pnpm deploy:bot-engine      # Deploy bot engine to Fly.io
#   pnpm deploy:webhook-handler # Deploy webhook handler to Vercel
#   pnpm deploy:all             # Deploy both services
#
# Deployment Script for Whatsite Bot - Handles both Fly.io and Vercel
# Comprehensive deployment with validation, monitoring, and rollback capabilities

set -e

# Configuration
BOT_ENGINE_APP_NAME="whatsite-bot-engine"
BOT_ENGINE_REGION="sjc"
BOT_ENGINE_DOCKERFILE_PATH="apps/bot-engine/Dockerfile"
BOT_ENGINE_FLY_TOML_PATH="apps/bot-engine/fly.production.toml"
BOT_ENGINE_HEALTH_CHECK_URL="https://${BOT_ENGINE_APP_NAME}.fly.dev/health"

WEBHOOK_HANDLER_PATH="apps/webhook-handler"
WEBHOOK_HANDLER_VERCEL_JSON="apps/webhook-handler/vercel.json"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    local missing_tools=()
    
    if ! command_exists fly; then
        missing_tools+=("Fly CLI")
    fi
    
    if ! command_exists vercel; then
        missing_tools+=("Vercel CLI")
    fi
    
    if ! command_exists docker; then
        missing_tools+=("Docker")
    fi
    
    if ! command_exists pnpm; then
        missing_tools+=("pnpm")
    fi
    
    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        log_error "Missing required tools: ${missing_tools[*]}"
        log_error "Please install the missing tools before proceeding."
        exit 1
    fi
    
    # Check if we're logged into Fly.io
    if ! fly auth whoami >/dev/null 2>&1; then
        log_error "Not logged into Fly.io. Please run 'fly auth login' first."
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

# Deploy bot engine to Fly.io
deploy_bot_engine() {
    log_info "Starting bot engine deployment to Fly.io..."
    
    # Check if configuration exists
    if [[ ! -f "$BOT_ENGINE_FLY_TOML_PATH" ]]; then
        log_error "Bot engine configuration not found: $BOT_ENGINE_FLY_TOML_PATH"
        exit 1
    fi
    
    # Navigate to bot engine directory
    cd apps/bot-engine
    
    # Install dependencies
    log_info "Installing bot engine dependencies..."
    pnpm install --frozen-lockfile
    
    # Build the application
    log_info "Building bot engine..."
    pnpm build
    
    # Deploy to Fly.io
    log_info "Deploying bot engine to Fly.io..."
    fly deploy --config fly.production.toml --build-arg NODE_ENV=production
    
    # Return to root directory
    cd ../..
    
    log_success "Bot engine deployed successfully to Fly.io"
}

# Deploy webhook handler to Vercel
deploy_webhook_handler() {
    log_info "Starting webhook handler deployment to Vercel..."
    
    # Check if configuration exists
    if [[ ! -f "$WEBHOOK_HANDLER_VERCEL_JSON" ]]; then
        log_error "Webhook handler configuration not found: $WEBHOOK_HANDLER_VERCEL_JSON"
        exit 1
    fi
    
    # Navigate to webhook handler directory
    cd apps/webhook-handler
    
    # Install dependencies
    log_info "Installing webhook handler dependencies..."
    pnpm install --frozen-lockfile
    
    # Deploy to Vercel
    log_info "Deploying webhook handler to Vercel..."
    vercel --prod
    
    # Return to root directory
    cd ../..
    
    log_success "Webhook handler deployed successfully to Vercel"
}

# Deploy both services
deploy_all() {
    log_info "Starting deployment of both services..."
    
    deploy_bot_engine
    deploy_webhook_handler
    
    log_success "🎉 All services deployed successfully!"
    log_info "Bot Engine: https://${BOT_ENGINE_APP_NAME}.fly.dev"
    log_info "Webhook Handler: Vercel serverless functions"
}

# Show usage
show_usage() {
    echo "Usage: $0 [bot-engine|webhook-handler|all]"
    echo ""
    echo "Commands:"
    echo "  bot-engine      Deploy only the bot engine to Fly.io"
    echo "  webhook-handler Deploy only the webhook handler to Vercel"
    echo "  all             Deploy both services (default)"
    echo ""
    echo "Examples:"
    echo "  $0 bot-engine"
    echo "  $0 webhook-handler"
    echo "  $0 all"
}

# Main function
main() {
    local command=${1:-all}
    
    log_info "Starting deployment process..."
    
    check_prerequisites
    
    case "$command" in
        "bot-engine")
            deploy_bot_engine
            ;;
        "webhook-handler")
            deploy_webhook_handler
            ;;
        "all")
            deploy_all
            ;;
        "--help"|"-h")
            show_usage
            exit 0
            ;;
        *)
            log_error "Invalid command: $command"
            show_usage
            exit 1
            ;;
    esac
}

# Run main function
main "$@"