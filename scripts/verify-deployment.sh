#!/bin/bash

# New deployment verification script using pnpm commands
# This replaces the old verify-deployment-separation.sh script

set -e

echo "🔍 Verifying deployment configuration..."

# Run the Node.js verification script
node scripts/verify-pnpm-deployment.js

echo ""
echo "🚀 Ready to deploy using pnpm commands:"
echo "  pnpm deploy:bot-engine      # Deploy bot engine to Fly.io"
echo "  pnpm deploy:webhook-handler # Deploy webhook handler to Vercel"
echo "  pnpm deploy:all             # Deploy both services"
echo "  pnpm deploy:verify          # Quick verification"