{"name": "@whatsite-bot/gemini-runner", "version": "0.1.0", "description": "Gemini CLI wrapper and AI integration utilities for WhatsApp Website Bot", "type": "module", "main": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist"], "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs", "types": "./dist/index.d.ts"}}, "scripts": {"build": "tsc -b", "clean": "rm -rf dist tsconfig.tsbuildinfo", "prepublishOnly": "npm run build", "dev": "tsc --watch", "test": "vitest", "test:run": "vitest run", "typecheck": "tsc --noEmit", "lint": "eslint . --ext .ts,.js,.cjs,.mjs", "lint:fix": "pnpm lint --fix", "format": "prettier --write ."}, "dependencies": {"@whatsite-bot/core": "workspace:*", "@google/genai": "^0.3.0", "openai": "^4.52.7"}, "devDependencies": {"@types/node": "^20.10.0", "typescript": "^5.3.0", "vitest": "^1.0.0", "prettier": "^3.0.0", "eslint": "^8.0.0"}, "publishConfig": {"access": "public"}}