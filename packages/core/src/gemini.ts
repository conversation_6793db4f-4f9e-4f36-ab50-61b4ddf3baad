import type { ChildProcess } from 'child_process';

import type { Workspace, WorkspaceProject, WorkspaceFile } from './workspace.js';

/**
 * Gemini session configuration
 */
export interface GeminiSessionConfig {
  /** Session ID */
  sessionId?: string;
  /** Workspace to operate in */
  workspace: Workspace;
  /** Working directory */
  workingDirectory?: string;
  /** Gemini CLI path */
  geminiCliPath?: string;
  /** Model to use */
  model?: string;
  /** Temperature setting */
  temperature?: number;
  /** Max tokens */
  maxTokens?: number;
  /** System prompt */
  systemPrompt?: string;
  /** Allowed tools */
  allowedTools?: string[];
  /** Session timeout in milliseconds */
  timeout?: number;
}

/**
 * Gemini session info
 */
export interface GeminiSession {
  /** Session ID */
  sessionId: string;
  /** Workspace */
  workspace: Workspace;
  /** Session start time */
  startedAt: Date;
  /** Whether session is running */
  isRunning: boolean;
  /** Child process */
  process?: ChildProcess;
  /** Session config */
  config: GeminiSessionConfig;
  /** Session messages */
  messages: GeminiMessage[];
  /** Session state */
  state: 'created' | 'active' | 'completed' | 'failed' | 'timeout';
  /** Error information */
  error?: string;
}

/**
 * Gemini message
 */
export interface GeminiMessage {
  /** Message ID */
  id: string;
  /** Message role */
  role: 'user' | 'assistant' | 'system';
  /** Message content */
  content: string;
  /** Message timestamp */
  timestamp: Date;
  /** Message metadata */
  metadata?: Record<string, unknown>;
}

/**
 * Project context for code generation
 */
export interface ProjectContext {
  /** Project configuration */
  project: WorkspaceProject;
  /** Workspace */
  workspace: Workspace;
  /** Existing files */
  files: string[];
  /** Project requirements */
  requirements: string;
  /** Technical constraints */
  constraints?: string[];
}

/**
 * Code generation result
 */
export interface CodeGenerationResult {
  /** Generated code */
  code: string;
  /** Generation description */
  description: string;
  /** Generated files */
  files: GeneratedFile[];
  /** Success status */
  success: boolean;
  /** Error message */
  error?: string;
}

/**
 * Generated file result
 */
export interface GeneratedFile {
  path: string;
  content: string;
  description: string;
  type: string;
}

/**
 * Project structure generated by AI
 */
export interface ProjectStructure {
  /** Project files and directories */
  files: ProjectFile[];
  /** Project configuration */
  config: Record<string, unknown>;
  /** Setup instructions */
  setupInstructions: string[];
  /** Dependencies to install */
  dependencies: string[];
}

/**
 * Project file in structure
 */
export interface ProjectFile {
  /** File path */
  path: string;
  /** File content */
  content: string;
  /** File type */
  type: 'file' | 'directory';
  /** File description */
  description?: string;
}

/**
 * Code analysis result
 */
export interface CodeAnalysis {
  /** Code quality score (0-100) */
  qualityScore: number;
  /** Issues found */
  issues: CodeIssue[];
  /** Suggestions for improvement */
  suggestions: string[];
  /** Code metrics */
  metrics: CodeMetrics;
}

/**
 * Code issue
 */
export interface CodeIssue {
  /** Issue type */
  type: 'error' | 'warning' | 'info';
  /** Issue message */
  message: string;
  /** Line number */
  line?: number;
  /** Column number */
  column?: number;
  /** Issue category */
  category: string;
  /** Suggested fix */
  fix?: string;
}

/**
 * Code metrics
 */
export interface CodeMetrics {
  /** Lines of code */
  linesOfCode: number;
  /** Cyclomatic complexity */
  complexity: number;
  /** Maintainability index */
  maintainability: number;
  /** Test coverage percentage */
  testCoverage?: number;
  /** Performance score */
  performance?: number;
}

/**
 * Improvement suggestion
 */
export interface Improvement {
  /** Improvement type */
  type: 'performance' | 'security' | 'accessibility' | 'seo' | 'code_quality';
  /** Improvement title */
  title: string;
  /** Improvement description */
  description: string;
  /** Implementation steps */
  steps: string[];
  /** Priority level */
  priority: 'low' | 'medium' | 'high';
  /** Estimated effort */
  effort: 'low' | 'medium' | 'high';
}

/**
 * Code generation context
 */
export interface CodeGenerationContext {
  project: WorkspaceProject;
  existingFiles: WorkspaceFile[];
  requirements: string;
  constraints?: string[];
  stylePreferences?: Record<string, unknown>;
}

/**
 * Gemini runner events
 */
export interface GeminiRunnerEvents {
  'session-started': (session: GeminiSession) => void;
  'session-ended': (sessionId: string) => void;
  message: (sessionId: string, message: GeminiMessage) => void;
  output: (sessionId: string, data: string) => void;
  error: (sessionId: string, error: string) => void;
  stream: (sessionId: string, chunk: string) => void;
}

/**
 * Gemini runner interface for AI-powered development
 */
export interface IGeminiRunner {
  initialize(workspace: Workspace): void;
  generateProjectStructure(prompt: string, project: WorkspaceProject): Promise<ProjectStructure>;
  generateCode(prompt: string, context: ProjectContext): Promise<CodeGenerationResult>;
  generateCodeFiles(prompt: string, context: CodeGenerationContext): Promise<GeneratedFile[]>;
  updateCode(filePath: string, content: string, instructions: string): Promise<string>;
  analyzeCode(filePath: string, content: string): Promise<CodeAnalysis>;
  generateDocumentation(project: WorkspaceProject): Promise<string>;
  fixCodeIssues(filePath: string, content: string, issues: string[]): Promise<string>;
  suggestImprovements(project: WorkspaceProject): Promise<Improvement[]>;
}
