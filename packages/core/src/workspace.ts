/**
 * Workspace and git worktree types for WhatsApp Website Bot
 * Based on Cyrus workspace patterns adapted for website development
 */

import type { User } from './types.js';

/**
 * Workspace configuration for website development
 */
export interface Workspace {
  /** Workspace ID */
  id: string;
  /** Workspace path on filesystem */
  path: string;
  /** Whether this is a git worktree */
  isGitWorktree: boolean;
  /** Base repository path (for worktrees) */
  basePath?: string;
  /** Git branch name */
  branch?: string;
  /** Workspace history path for session data */
  historyPath?: string;
  /** Workspace type */
  type: WorkspaceType;
  /** User who owns the workspace */
  user: User;
  /** Project configuration */
  project?: WorkspaceProject;
  /** Creation timestamp */
  createdAt: Date;
  /** Last accessed timestamp */
  lastAccessedAt: Date;
  /** Whether workspace is active */
  isActive: boolean;
  /** Workspace metadata */
  metadata?: Record<string, unknown>;
}

/**
 * Workspace type enumeration
 */
export type WorkspaceType = 'temporary' | 'persistent' | 'template' | 'import';

/**
 * Project configuration within a workspace
 */
export interface WorkspaceProject {
  /** Project name */
  name: string;
  /** Project description */
  description?: string;
  /** Project type */
  type: ProjectType;
  /** Technology stack */
  stack: TechStack;
  /** Project configuration */
  config: ProjectConfig;
  /** Build configuration */
  build?: BuildConfig;
  /** Deployment configuration */
  deployment?: DeploymentConfig;
}

/**
 * Project type enumeration
 */
export type ProjectType =
  | 'static'
  | 'spa'
  | 'react'
  | 'vue'
  | 'angular'
  | 'nextjs'
  | 'nuxt'
  | 'svelte'
  | 'custom';

/**
 * Technology stack configuration
 */
export interface TechStack {
  /** Frontend framework */
  framework?: string;
  /** CSS framework or preprocessor */
  styling?: string;
  /** Build tool */
  buildTool?: string;
  /** Package manager */
  packageManager?: string;
  /** Runtime environment */
  runtime?: string;
  /** Additional dependencies */
  dependencies?: string[];
}

/**
 * Project configuration
 */
export interface ProjectConfig {
  /** Source directory */
  srcDir: string;
  /** Build output directory */
  buildDir: string;
  /** Public/static files directory */
  publicDir: string;
  /** Entry point file */
  entryPoint: string;
  /** Additional configuration files */
  configFiles?: string[];
  /** Environment variables */
  env?: Record<string, string>;
}

/**
 * Build configuration
 */
export interface BuildConfig {
  /** Build command */
  command: string;
  /** Build output directory */
  outputDir: string;
  /** Build environment */
  env?: Record<string, string>;
  /** Build options */
  options?: Record<string, unknown>;
}

/**
 * Deployment configuration
 */
export interface DeploymentConfig {
  /** Deployment platform */
  platform: string;
  /** Deployment configuration */
  config: Record<string, unknown>;
  /** Environment variables */
  env?: Record<string, string>;
  /** Custom domain */
  domain?: string;
}

/**
 * Workspace creation options
 */
export interface WorkspaceOptions {
  /** User creating the workspace */
  user: User;
  /** Workspace type */
  type: WorkspaceType;
  /** Base path for workspace creation */
  basePath?: string;
  /** Git repository URL (for imports) */
  gitUrl?: string;
  /** Git branch name */
  branch?: string;
  /** Project configuration */
  project?: Partial<WorkspaceProject>;
  /** Workspace metadata */
  metadata?: Record<string, unknown>;
}

/**
 * Git worktree configuration
 */
export interface GitWorktreeConfig {
  /** Base repository path */
  basePath: string;
  /** Worktree path */
  worktreePath: string;
  /** Branch name */
  branch: string;
  /** Remote repository URL */
  remoteUrl?: string;
  /** Whether to track remote branch */
  trackRemote?: boolean;
}

/**
 * Workspace manager interface for managing workspaces
 */
export interface IWorkspaceManager {
  /** Create a new workspace */
  createWorkspace(options: WorkspaceOptions): Promise<Workspace>;
  /** Get workspace by ID */
  getWorkspace(id: string): Promise<Workspace | null>;
  /** Get workspaces by user */
  getWorkspacesByUser(phoneNumber: string): Promise<Workspace[]>;
  /** Update workspace */
  updateWorkspace(workspace: Workspace): Promise<void>;
  /** Delete workspace */
  deleteWorkspace(id: string): Promise<void>;
  /** Create git worktree */
  createGitWorktree(config: GitWorktreeConfig): Promise<Workspace>;
  /** Initialize project in workspace */
  initializeProject(workspace: Workspace, project: WorkspaceProject): Promise<void>;
  /** Clone repository into workspace */
  cloneRepository(workspace: Workspace, gitUrl: string, branch?: string): Promise<void>;
  /** Build project in workspace */
  buildProject(workspace: Workspace): Promise<BuildResult>;
  /** Deploy project from workspace */
  deployProject(workspace: Workspace): Promise<DeploymentResult>;
  /** Clean up workspace */
  cleanupWorkspace(workspace: Workspace): Promise<void>;
  /** Get workspace status */
  getWorkspaceStatus(workspace: Workspace): Promise<WorkspaceStatus>;
  /** List workspace files */
  listWorkspaceFiles(workspace: Workspace, directory?: string): Promise<WorkspaceFile[]>;
  /** Read workspace file */
  readWorkspaceFile(workspace: Workspace, filePath: string): Promise<string>;
  /** Write workspace file */
  writeWorkspaceFile(workspace: Workspace, filePath: string, content: string): Promise<void>;
  /** Delete workspace file */
  deleteWorkspaceFile(workspace: Workspace, filePath: string): Promise<void>;
}

/**
 * Build result
 */
export interface BuildResult {
  /** Whether build was successful */
  success: boolean;
  /** Build output */
  output?: string;
  /** Build errors */
  errors?: string[];
  /** Build warnings */
  warnings?: string[];
  /** Build duration in milliseconds */
  duration: number;
  /** Output files */
  files?: string[];
}

/**
 * Deployment result
 */
export interface DeploymentResult {
  /** Whether deployment was successful */
  success: boolean;
  /** Deployment URL */
  url?: string;
  /** Deployment logs */
  logs?: string[];
  /** Deployment errors */
  errors?: string[];
  /** Deployment duration in milliseconds */
  duration: number;
  /** Deployment metadata */
  metadata?: Record<string, unknown>;
}

/**
 * Workspace status
 */
export interface WorkspaceStatus {
  /** Whether workspace exists */
  exists: boolean;
  /** Whether workspace is accessible */
  accessible: boolean;
  /** Git status (if git repository) */
  git?: GitStatus;
  /** Disk usage in bytes */
  diskUsage?: number;
  /** Last modified time */
  lastModified?: Date;
  /** Number of files */
  fileCount?: number;
  /** Project status */
  project?: ProjectStatus;
}

/**
 * Git status information
 */
export interface GitStatus {
  /** Current branch */
  branch?: string;
  /** Remote URL */
  remoteUrl?: string;
  /** Whether repository is clean */
  isClean: boolean;
  /** Modified files */
  modifiedFiles?: string[];
  /** Untracked files */
  untrackedFiles?: string[];
  /** Staged files */
  stagedFiles?: string[];
  /** Latest commit SHA */
  latestCommit?: string;
}

/**
 * Project status information
 */
export interface ProjectStatus {
  /** Whether project is initialized */
  initialized: boolean;
  /** Whether project can be built */
  buildable: boolean;
  /** Whether project can be deployed */
  deployable: boolean;
  /** Missing dependencies */
  missingDependencies?: string[];
  /** Configuration issues */
  configIssues?: string[];
}

/**
 * Workspace file information
 */
export interface WorkspaceFile {
  /** File name */
  name: string;
  /** File path relative to workspace */
  path: string;
  /** File type */
  type: 'file' | 'directory';
  /** File size in bytes (for files) */
  size?: number;
  /** Last modified time */
  lastModified: Date;
  /** File permissions */
  permissions?: string;
  /** Whether file is binary */
  isBinary?: boolean;
}
