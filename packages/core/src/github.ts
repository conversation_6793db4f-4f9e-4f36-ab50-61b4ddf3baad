/**
 * GitHub API response types for the WhatsApp Website Bot
 */

/**
 * Basic interface for GitHub Repository API response.
 * This is a simplified representation of Octokit's response.
 */
export interface GitHubRepoResponse {
  data: {
    full_name: string;
    description: string | null;
    html_url: string;
    clone_url: string;
    default_branch: string;
    created_at: string;
    updated_at: string;
    name: string; // Added name property
  };
}

/**
 * Basic interface for GitHub Content API response.
 * This is a simplified representation of Octokit's response.
 */
export interface GitHubContentResponse {
  data: {
    content: string;
    encoding: string;
  };
}

/**
 * Interface for file operations in a Git repository.
 */
export interface FileOperation {
  path: string;
  content?: string;
  operation: 'create' | 'update' | 'delete';
}
