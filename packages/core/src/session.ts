/**
 * Session management types for WhatsApp Website Bot
 * Based on Cyrus session patterns adapted for WhatsApp use cases
 */

import type { ChildProcess } from 'child_process';

import type { Workspace } from './workspace.js';
import type { User, UserPreferences } from './types.js';

/**
 * Session states for website creation/editing/import operations
 */
export type SessionState = 'created' | 'active' | 'completed' | 'failed' | 'timeout';

/**
 * Session operation types
 */
export type SessionOperation = 'new_site' | 'edit_site' | 'import_repo' | 'scaffold_mode';

/**
 * Session context for maintaining conversation state
 */
export interface SessionContext {
  /** Previous messages in the conversation */
  messages: SessionMessage[];
  /** Current user intent */
  intent?: string;
  /** Project metadata */
  project?: ProjectMetadata;
  /** User preferences */
  preferences?: UserPreferences;
  /** Any additional context data */
  data?: Record<string, unknown>;
}

/**
 * Session message for conversation history
 */
export interface SessionMessage {
  /** Message ID */
  id: string;
  /** Message role (user, assistant, system) */
  role: 'user' | 'assistant' | 'system';
  /** Message content */
  content: string;
  /** Message timestamp */
  timestamp: Date;
  /** Message metadata */
  metadata?: Record<string, unknown>;
}

/**
 * Project metadata for session context
 */
export interface ProjectMetadata {
  [key: string]: unknown; // Add index signature
  /** Project name */
  name: string;
  /** Project description */
  description?: string;
  /** Project URL */
  url?: string | null;
  /** GitHub repository information */
  repository?: {
    url: string;
    name: string;
    owner: string;
    branch: string;
  };
  /** Technology stack */
  stack?: string[];
  /** Project features */
  features?: string[];
}

/**
 * Session options for creating a new session
 */
export interface SessionOptions {
  /** Session ID */
  id?: string;
  /** User information */
  user: User;
  /** Session operation type */
  operation: SessionOperation;
  /** Workspace configuration */
  workspace: Workspace;
  /** Initial session context */
  context?: SessionContext;
  /** Session timeout in milliseconds */
  timeout?: number;
  /** Process reference for active operations */
  process?: ChildProcess | null;
  /** Session start time */
  startedAt?: Date;
  /** Session end time */
  endedAt?: Date | null;
  /** Exit code for completed sessions */
  exitCode?: number | null;
  /** Error information */
  error?: string | null;
  /** Last assistant response */
  lastResponse?: string;
  /** Streaming state */
  streaming?: StreamingState;
  /** Session metadata */
  metadata?: Record<string, unknown>;
}

/**
 * Streaming state for real-time updates
 */
export interface StreamingState {
  /** Current streaming status */
  active: boolean;
  /** Streaming message ID */
  messageId?: string;
  /** Current streaming content */
  content?: string;
  /** Streaming narrative items */
  narrative: NarrativeItem[];
  /** Streaming synthesis */
  synthesis?: string;
}

/**
 * Narrative item for streaming updates
 */
export interface NarrativeItem {
  /** Item type */
  type: 'text' | 'tool_call' | 'file_operation' | 'deployment' | 'error';
  /** Item content */
  content?: string;
  /** Tool name for tool_call type */
  tool?: string;
  /** File path for file_operation type */
  file?: string;
  /** Operation for file_operation type */
  operation?: 'create' | 'update' | 'delete' | 'read';
  /** Timestamp */
  timestamp: number;
  /** Additional metadata */
  metadata?: Record<string, unknown>;
}

/**
 * Website session class for managing WhatsApp bot interactions
 */
export interface IWebsiteSession {
  readonly id: string;
  readonly user: User;
  readonly operation: SessionOperation;
  readonly workspace: Workspace;
  state: SessionState;
  context: SessionContext;
  readonly timeoutMs: number;
  process: ChildProcess | null;
  readonly startedAt: Date;
  endedAt: Date | null;
  exitCode: number | null;
  error: string | null;
  lastResponse: string;
  streaming: StreamingState;

  isActive(): boolean;
  isCompleted(): boolean;
  hasFailed(): boolean;
  hasTimedOut(): boolean;
  start(): void;
  complete(): void;
  fail(error: string): void;
  timeout(): void;
  addMessage(message: Omit<SessionMessage, 'id' | 'timestamp'>): void;
  addNarrativeItem(item: Omit<NarrativeItem, 'timestamp'>): void;
  resetStreamingState(): void;
  getSummary(): SessionSummary;
}

export class WebsiteSession implements IWebsiteSession {
  /** Session ID */
  public readonly id: string;
  /** User information */
  public readonly user: User;
  /** Session operation */
  public readonly operation: SessionOperation;
  /** Workspace configuration */
  public readonly workspace: Workspace;
  /** Session state */
  public state: SessionState;
  /** Session context */
  public context: SessionContext;
  /** Session timeout in milliseconds */
  public readonly timeoutMs: number;
  /** Process reference */
  public process: ChildProcess | null;
  /** Session start time */
  public readonly startedAt: Date;
  /** Session end time */
  public endedAt: Date | null;
  /** Exit code */
  public exitCode: number | null;
  /** Error information */
  public error: string | null;
  /** Last assistant response */
  public lastResponse: string;
  /** Streaming state */
  public streaming: StreamingState;

  constructor(options: SessionOptions) {
    this.id = options.id || this.generateSessionId();
    this.user = options.user; // eslint-disable-line @typescript-eslint/no-unsafe-assignment
    this.operation = options.operation;
    this.workspace = options.workspace; // eslint-disable-line @typescript-eslint/no-unsafe-assignment
    this.state = 'created';
    this.context = options.context || { messages: [] };
    this.timeoutMs = options.timeout || 30 * 60 * 1000; // 30 minutes default
    this.process = options.process || null;
    this.startedAt = options.startedAt || new Date();
    this.endedAt = options.endedAt || null;
    this.exitCode = options.exitCode || null;
    this.error = options.error || null;
    this.lastResponse = options.lastResponse || '';
    this.streaming = options.streaming || {
      active: false,
      narrative: [],
    };
  }

  /**
   * Generate a unique session ID
   */
  private generateSessionId(): string {
    return `ws_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Check if session is active
   */
  isActive(): boolean {
    return this.state === 'active' && this.process !== null && !this.process.killed;
  }

  /**
   * Check if session is completed successfully
   */
  isCompleted(): boolean {
    return this.state === 'completed' && this.exitCode === 0;
  }

  /**
   * Check if session has failed
   */
  hasFailed(): boolean {
    return this.state === 'failed' || (this.exitCode !== null && this.exitCode !== 0);
  }

  /**
   * Check if session has timed out
   */
  hasTimedOut(): boolean {
    return (
      this.state === 'timeout' ||
      (this.startedAt && Date.now() - this.startedAt.getTime() > this.timeoutMs)
    );
  }

  /**
   * Start the session
   */
  start(): void {
    if (this.state !== 'created') {
      throw new Error(`Cannot start session in state: ${this.state}`);
    }
    this.state = 'active';
  }

  /**
   * Complete the session successfully
   */
  complete(): void {
    if (this.state !== 'active') {
      throw new Error(`Cannot complete session in state: ${this.state}`);
    }
    this.state = 'completed';
    this.endedAt = new Date();
    this.exitCode = 0;
  }

  /**
   * Fail the session with error
   */
  fail(error: string): void {
    this.state = 'failed';
    this.endedAt = new Date();
    this.error = error;
    this.exitCode = 1;
  }

  /**
   * Mark session as timed out
   */
  timeout(): void {
    this.state = 'timeout';
    this.endedAt = new Date();
    this.error = 'Session timed out';
    this.exitCode = 124; // Standard timeout exit code
  }

  /**
   * Add a message to the session context
   */
  addMessage(message: Omit<SessionMessage, 'id' | 'timestamp'>): void {
    const sessionMessage: SessionMessage = {
      ...message,
      id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
    };
    this.context.messages.push(sessionMessage);
  }

  /**
   * Add a narrative item for streaming
   */
  addNarrativeItem(item: Omit<NarrativeItem, 'timestamp'>): void {
    const narrativeItem: NarrativeItem = {
      ...item,
      timestamp: Date.now(),
    };
    this.streaming.narrative.push(narrativeItem);
    this.updateStreamingSynthesis();
  }

  /**
   * Update streaming synthesis based on narrative
   */
  private updateStreamingSynthesis(): void {
    const entries: string[] = [];

    for (const item of this.streaming.narrative) {
      switch (item.type) {
        case 'text':
          if (item.content) {
            entries.push(this.extractTextPreview(item.content));
          }
          break;
        case 'tool_call':
          if (item.tool) {
            entries.push(`Using tool: ${item.tool}`);
          }
          break;
        case 'file_operation':
          if (item.file && item.operation) {
            entries.push(`${item.operation} ${item.file}`);
          }
          break;
        case 'deployment':
          entries.push('Deploying to production');
          break;
        case 'error':
          if (item.content) {
            entries.push(`Error: ${item.content}`);
          }
          break;
      }
    }

    this.streaming.synthesis =
      entries.length > 0
        ? `Working on your ${this.operation} request...\n\n${entries.join('\n')}`
        : `Working on your ${this.operation} request...`;
  }

  /**
   * Extract text preview from content
   */
  private extractTextPreview(text: string): string {
    if (!text) return '';
    const cleaned = text.replace(/\s+/g, ' ').trim();
    return cleaned.length > 80 ? cleaned.substring(0, 77) + '...' : cleaned;
  }

  /**
   * Reset streaming state
   */
  resetStreamingState(): void {
    this.streaming = {
      active: false,
      narrative: [],
    };
  }

  /**
   * Get session summary
   */
  getSummary(): SessionSummary {
    return {
      id: this.id,
      user: this.user, // eslint-disable-line @typescript-eslint/no-unsafe-assignment
      operation: this.operation,
      state: this.state,
      startedAt: this.startedAt,
      endedAt: this.endedAt,
      duration: this.endedAt ? this.endedAt.getTime() - this.startedAt.getTime() : null,
      exitCode: this.exitCode,
      error: this.error,
      messageCount: this.context.messages.length,
      projectName: this.context.project?.name,
    };
  }
}

/**
 * Session summary for overview purposes
 */
export interface SessionSummary {
  /** Session ID */
  id: string;
  /** User information */
  user: User;
  /** Session operation */
  operation: SessionOperation;
  /** Session state */
  state: SessionState;
  /** Start time */
  startedAt: Date;
  /** End time */
  endedAt: Date | null;
  /** Session duration in milliseconds */
  duration: number | null;
  /** Exit code */
  exitCode: number | null;
  /** Error message */
  error: string | null;
  /** Number of messages */
  messageCount: number;
  /** Project name */
  projectName?: string;
}

/**
 * Session manager interface
 */
export interface ISessionManager {
  /** Create a new session */
  createSession(options: SessionOptions): Promise<WebsiteSession>;
  /** Get session by ID */
  getSession(id: string): Promise<WebsiteSession | null>;
  /** Get sessions by user */
  getSessionsByUser(phoneNumber: string): Promise<WebsiteSession[]>;
  /** Update session */
  updateSession(session: WebsiteSession): Promise<void>;
  /** Delete session */
  deleteSession(id: string): Promise<void>;
  /** Get active sessions */
  getActiveSessions(): Promise<WebsiteSession[]>;
  /** Clean up expired sessions */
  cleanupExpiredSessions(): Promise<void>;
}
