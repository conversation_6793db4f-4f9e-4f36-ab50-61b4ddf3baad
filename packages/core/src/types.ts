/**
 * Core type definitions for the WhatsApp Website Bot
 */

/**
 * User information for the bot
 */
export interface User {
  /** User's phone number (primary identifier) */
  phoneNumber: string;
  /** User's display name */
  name?: string;
  /** User's profile picture URL */
  profilePicture?: string;
  /** User's preferred language */
  language?: string;
  /** User registration timestamp */
  registeredAt?: Date;
  /** User's last activity timestamp */
  lastActiveAt?: Date;
  /** Whether user is active */
  isActive?: boolean;
  /** User preferences */
  preferences?: UserPreferences;
  /** User metadata */
  metadata?: Record<string, unknown>;
}

/**
 * User preferences
 */
export interface UserPreferences extends Record<string, unknown> {
  /** Preferred programming language */
  language?: string;
  /** Preferred framework */
  framework?: string;
  /** Preferred styling approach */
  styling?: string;
  /** Deployment preferences */
  deployment?: {
    platform: string;
    domain?: string;
  };
  /** Notification preferences */
  notifications?: {
    completion: boolean;
    errors: boolean;
    progress: boolean;
  };
}

export interface WhatsAppMessage {
  From: string;
  Body: string;
  NumMedia: number;
  MediaUrl0?: string;
}

export interface ProjectData {
  projectName: string;
  description: string;
  url?: string | null;
  htmlContent: string;
  githubRepoUrl?: string | null;
  githubRepoName?: string | null;
  lastCommitSha?: string | null;
}

export interface UserProject {
  id: number;
  phone_number: string;
  project_name: string;
  description: string;
  url: string;
  html_content: string;
  github_repo_url?: string | null;
  github_repo_name?: string | null;
  last_commit_sha?: string | null;
  is_active: boolean;
  created_at: string;
  updated_at?: string;
}

export interface IntentClassification {
  intent: 'new_site' | 'edit_site' | 'import_repo' | 'scaffold_mode';
  confidence: number;
  reasoning: string;
  repositoryInfo?: RepositoryInfo;
}

export interface RepositoryInfo {
  type: 'url' | 'name';
  fullUrl?: string | null;
  repoName?: string | null;
  owner?: string;
  repo?: string;
  repoIdentifier?: string;
}

export interface StatusMessageData {
  [key: string]: unknown;
  text?: string;
  transcript?: string;
  intent?: string;
  description?: string;
  projectName?: string;
  url?: string;
  repoName?: string;
  error?: string;
  message?: string;
}

export interface GitHubFileContent {
  name: string;
  path: string;
  content: string;
  type: 'file' | 'dir';
}

export interface GitHubCommitData {
  message: string;
  tree: string;
  parents: string[];
}

export interface DeploymentResponse {
  url?: string;
  alias?: string[];
  name?: string;
  readyState?: string;
  error?: {
    message: string;
  };
}

export interface EnvironmentValidation {
  isValid: boolean;
  missing: string[];
  envVars: Record<string, boolean>;
}

export interface GitHubRepositoryData {
  repoUrl: string;
  repoName: string;
  cloneUrl: string;
  defaultBranch: string;
}

export interface GitHubFileData {
  content: string;
  sha: string;
  size: number;
}

export interface GitHubCommitInfo {
  sha: string;
  html_url: string;
}

export interface GitHubCreateResponse {
  commit: GitHubCommitInfo;
}

export interface RepositoryValidation {
  isValid: boolean;
  error?: string;
  message?: string;
  repoData?: {
    fullName: string;
    description: string | null;
    htmlUrl: string;
    cloneUrl: string;
    defaultBranch: string;
    createdAt: string;
    updatedAt: string;
  };
}

export interface RepositoryMetadata {
  fullName: string;
  name: string;
  description: string;
  htmlUrl: string;
  cloneUrl: string;
  defaultBranch: string;
  createdAt: string;
  updatedAt: string;
  latestCommitSha: string | null;
}

export interface GitHubCreateOptions {
  genAI?: unknown;
  userMessage?: string;
}

export interface GitHubUpdateOptions {
  userMessage?: string;
  oldContent?: string;
  projectName?: string;
  genAI?: unknown;
}

// Type aliases for consistency
export type GitHubValidationResult = RepositoryValidation;

export interface GitHubImportData {
  projectName: string;
  description: string;
  htmlContent: string;
  githubRepoUrl: string;
  githubRepoName: string;
  lastCommitSha: string | null;
  url: string | null;
}

/**
 * Configuration for the WebsiteManager.
 */
export interface WebsiteManagerConfig {
  enableGitIntegration: boolean;
  enableDeployment: boolean;
  workspaceDir: string;
  sessionTimeout?: number; // Added sessionTimeout
  github: {
    token: string;
    organization?: string;
  };
  vercel: {
    token: string;
  };
}

/**
 * Context for creating a new website.
 */
export interface WebsiteCreationContext {
  prompt: string;
  projectName: string;
  preferences?: UserPreferences;
  stack: string[];
  features: string[];
}

/**
 * Context for editing an existing website.
 */
export interface WebsiteEditContext {
  instructions: string;
  currentContent?: string;
  targetFiles: string[];
  editType: 'content' | 'code' | 'structure';
}

/**
 * Result of an import operation.
 */
export type ImportResult =
  | {
      success: true;
      projectName: string;
      repositoryUrl: string;
      deploymentUrl: string;
      description?: string;
    }
  | {
      success: false;
      projectName: string;
      repositoryUrl: string;
      deploymentUrl: string;
      error: string;
    };

// Configuration types
export interface BotConfig {
  twilio: {
    accountSid: string;
    authToken: string;
    phoneNumber: string;
  };
  openai: {
    apiKey: string;
  };
  google: {
    apiKey: string;
  };
  vercel: {
    token: string;
    teamId?: string;
  };
  supabase: {
    url: string;
    anonKey: string;
  };
  github: {
    token: string;
  };
}

// WhatsApp Business API types (different from Twilio format)
export interface WhatsAppWebhook {
  entry: WhatsAppWebhookEntry[];
  id?: string;
}

export interface WhatsAppWebhookEntry {
  id: string;
  changes: WhatsAppWebhookChange[];
}

export interface WhatsAppWebhookChange {
  field: string;
  value: {
    messages?: WhatsAppApiMessage[];
    contacts?: WhatsAppContact[];
    metadata?: {
      phone_number_id: string;
      display_phone_number: string;
    };
  };
}

export interface WhatsAppApiMessage {
  id: string;
  from: string;
  timestamp: string;
  type: string;
  text?: {
    body: string;
  };
  button?: {
    text: string;
    payload: string;
  };
  interactive?: {
    type: string;
    button_reply?: {
      id: string;
      title: string;
    };
    list_reply?: {
      id: string;
      title: string;
      description?: string;
    };
  };
  contact?: WhatsAppContact;
  metadata?: {
    entry_id: string;
    business_phone_number_id: string;
    webhook_id: string;
  };
}

export interface WhatsAppContact {
  wa_id: string;
  profile?: {
    name: string;
  };
}

// Intent classification types
export type IntentType = 'website_creation' | 'help' | 'greeting' | 'status' | 'other';

export interface ClassifiedIntent {
  type: IntentType;
  confidence: number;
  reasoning: string;
  suggestedResponse: string;
}

// Webhook forwarding types
export interface WebhookForwardingResult {
  success: boolean;
  statusCode?: number;
  response?: unknown;
  attempt?: number;
  error?: string;
  attempts?: number;
  lastError?: Error;
}
