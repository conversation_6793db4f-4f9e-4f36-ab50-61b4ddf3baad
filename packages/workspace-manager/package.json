{"name": "@whatsite-bot/workspace-manager", "version": "0.1.0", "description": "Git worktree management utilities for WhatsApp Website Bot", "type": "module", "main": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist"], "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs", "types": "./dist/index.d.ts"}}, "scripts": {"build": "tsc -b", "dev": "tsc --watch", "test": "vitest", "test:run": "vitest run", "typecheck": "tsc --noEmit", "clean": "rm -rf dist tsconfig.tsbuildinfo", "prepublishOnly": "npm run build", "lint": "eslint . --ext .ts,.js,.cjs,.mjs", "lint:fix": "pnpm lint --fix", "format": "prettier --write ."}, "dependencies": {"@whatsite-bot/core": "workspace:*", "simple-git": "^3.20.0"}, "devDependencies": {"@types/node": "^20.10.0", "typescript": "^5.3.0", "vitest": "^1.0.0", "prettier": "^3.0.0", "eslint": "^8.0.0"}, "publishConfig": {"access": "public"}}