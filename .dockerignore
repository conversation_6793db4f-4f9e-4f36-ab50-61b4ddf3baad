# Exclude webhook handler and related files
apps/webhook-handler/
apps/webhook-handler/**/*

# Exclude development files
.git/
.gitignore
node_modules/
**/node_modules/
.pnpm-store/
.vscode/
.idea/
*.log
*.tmp
*.temp
.env.local
.env.development
.env.test
coverage/
.nyc_output/
build/
**/build/

# Exclude documentation and non-essential files
README.md
docs/
*.md
!apps/bot-engine/README.md

# Exclude test files
tests/
**/*.test.js
**/*.test.ts
**/*.spec.js
**/*.spec.ts
**/__tests__/
**/__mocks__/

# Exclude CI/CD files (except what's needed)
.github/
.vscode/
scripts/verify-*
scripts/deploy-*
scripts/test-*

# Exclude deployment configs for other platforms
vercel.json
apps/webhook-handler/vercel.json

# Exclude backup and temporary files
*.backup
*.bak
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Exclude package manager cache
.pnpm-debug.log*
.npm/
.yarn/
.pnpm-store/

# Exclude environment files
.env
.env.*
!.env.example

# Exclude build artifacts
*.tsbuildinfo
.next/
.nuxt/
.cache/
.parcel-cache/

# Exclude coverage reports
coverage/
.nyc_output/
.coverage/

# Exclude editor files
.vscode/
.idea/
*.sublime-project
*.sublime-workspace

# Exclude OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Exclude logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Exclude runtime files
*.pid
*.seed
*.pid.lock

# Exclude dependency directories
jspm_packages/

# Exclude TypeScript cache
*.tsbuildinfo

# Exclude optional npm cache directory
.npm

# Exclude optional eslint cache
.eslintcache

# Exclude microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Exclude optional REPL history
.node_repl_history

# Exclude output of 'npm pack'
*.tgz

# Exclude Yarn Integrity file
.yarn-integrity

# Exclude dotenv environment variables file
.env.test

# Exclude parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Exclude next.js build output
.next

# Exclude nuxt.js build output
.nuxt

# Exclude vuepress build output
.vuepress/dist

# Exclude Serverless directories
.serverless/

# Exclude FuseBox cache
.fusebox/

# Exclude DynamoDB Local files
.dynamodb/

# Exclude TernJS port file
.tern-port

# Exclude Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Exclude yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# Exclude turbo
.turbo/

# Exclude storybook
storybook-static/