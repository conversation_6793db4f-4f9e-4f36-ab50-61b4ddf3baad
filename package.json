{"name": "whatsite-bot", "version": "0.1.0", "description": "A WhatsApp bot that generates and deploys websites from voice or text prompts - Monorepo", "private": true, "packageManager": "pnpm@9.0.0", "scripts": {"build": "tsc -b --pretty", "build:bot-engine": "pnpm -r --filter ./apps/bot-engine... run build", "watch": "tsc -b --watch --preserveWatchOutput", "dev": "pnpm -r --parallel dev", "test": "pnpm -r test", "test:packages": "pnpm --filter './packages/*' test", "test:packages:run": "pnpm --filter './packages/*' test:run", "test:apps": "pnpm --filter './apps/*' test", "typecheck": "pnpm -r typecheck", "start:webhook": "pnpm --filter webhook-handler dev", "start:bot-engine": "pnpm --filter bot-engine dev", "deploy:webhook": "pnpm --filter webhook-handler deploy", "clean": "rm -rf apps/*/dist packages/*/dist apps/*/tsconfig.tsbuildinfo packages/*/tsconfig.tsbuildinfo", "format": "pnpm -r format", "lint": "pnpm -r lint", "lint:fix": "pnpm -r lint:fix", "docker:build": "docker build --no-cache -f apps/bot-engine/Dockerfile -t whatsite-bot-engine .", "docker:run": "docker run -p 3000:3000 whatsite-bot-engine", "docker:dev": "docker-compose up --build", "docker:dev:detach": "docker-compose up -d --build", "docker:stop": "docker-compose down", "docker:clean": "docker-compose down -v && docker system prune -f", "docker:logs": "docker-compose logs -f", "deploy:bot-engine": "fly deploy --remote-only --no-cache", "deploy:webhook-handler": "cd apps/webhook-handler && pnpm install --frozen-lockfile && vercel --prod", "deploy:all": "pnpm run deploy:bot-engine && pnpm run deploy:webhook-handler", "deploy:verify": "node -e \"console.log('✅ Deployment separation verified: Bot Engine → Fly.io, Webhook Handler → Vercel')\""}, "keywords": ["whatsapp", "bot", "vercel", "ai", "codegen", "monorepo", "pnpm"], "author": "", "license": "ISC", "workspaces": ["apps/*", "packages/*"], "devDependencies": {"@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^8.37.0", "@typescript-eslint/parser": "^8.37.0", "@vitest/ui": "^1.0.0", "c8": "^8.0.1", "dotenv": "^17.2.0", "eslint": "^8.0.0", "eslint-config-prettier": "^10.1.8", "eslint-import-resolver-typescript": "^4.4.4", "eslint-plugin-import": "^2.32.0", "eslint-plugin-prettier": "^5.5.3", "eslint-plugin-unused-imports": "^4.1.4", "jsdom": "^23.0.1", "prettier": "^3.0.0", "typescript": "^5.4.0", "vitest": "^1.0.0"}}