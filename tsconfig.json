// {
//   "extends": "./tsconfig.base.json",
//   "compilerOptions": {
//     "allowJs": true,
//     "checkJs": false,
//     "declaration": true,
//     "outDir": "./dist",
//     "rootDir": "./",
//     "removeComments": true,
//     "strict": false,
//     "noImplicitAny": false,
//     "strictNullChecks": true,
//     "strictFunctionTypes": true,
//     "noEmit": true
//   },
//   "include": [
//     "**/*.ts",
//     "**/*.js"
// , "scripts/indexRepo.mjs"  ],
//   "exclude": [
//     "node_modules",
//     "dist",
//     "tests/**/*.test.js"
//   ]
// }

{
  "files": [],
  "references": [
    { "path": "packages/core" },
    { "path": "packages/gemini-runner" },
    { "path": "packages/workspace-manager" },
    { "path": "apps/bot-engine" },
    { "path": "apps/webhook-handler" }
  ]
}