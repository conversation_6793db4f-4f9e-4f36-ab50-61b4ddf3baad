{"name": "@whatsite-bot/bot-engine", "version": "0.1.0", "description": "WhatsApp bot engine for Fly.io deployment", "private": true, "sideEffects": false, "type": "module", "main": "./dist/core/index.js", "types": "./dist/core/index.d.ts", "scripts": {"dev": "tsx src/core/index.ts", "build": "tsc -b", "start": "node dist/core/index.js", "deploy": "echo 'Use pnpm deploy:bot-engine from root directory instead'", "deploy:staging": "echo 'Use pnpm deploy:bot-engine from root directory instead'", "deploy:production": "echo 'Use pnpm deploy:bot-engine from root directory instead'", "deploy:quick": "echo 'Use pnpm deploy:bot-engine from root directory instead'", "monitoring:setup": "../../scripts/monitoring-setup.sh", "fly:deploy": "echo 'Use pnpm deploy:bot-engine from root directory instead'", "fly:deploy:production": "echo 'Use pnpm deploy:bot-engine from root directory instead'", "fly:logs": "fly logs", "fly:status": "fly status", "fly:scale": "fly scale", "fly:secrets": "fly secrets list", "health:check": "curl -s https://whatsite-bot-engine.fly.dev/health | jq", "health:ready": "curl -s https://whatsite-bot-engine.fly.dev/ready | jq", "health:startup": "curl -s https://whatsite-bot-engine.fly.dev/startup | jq", "metrics": "curl -s https://whatsite-bot-engine.fly.dev/metrics", "test": "vitest run", "test:watch": "vitest --watch", "typecheck": "tsc --noEmit", "clean": "rm -rf dist", "format": "prettier --write .", "lint": "eslint . --ext .ts,.js,.cjs,.mjs", "lint:fix": "pnpm lint --fix", "docker:build": "docker build -t whatsite-bot-engine .", "docker:run": "docker run -p 3000:3000 whatsite-bot-engine", "docker:dev": "docker-compose up --build", "docker:dev:detach": "docker-compose up -d --build", "docker:stop": "docker-compose down", "docker:clean": "docker-compose down -v && docker system prune -f", "docker:logs": "docker-compose logs -f bot-engine"}, "dependencies": {"@google/genai": "^0.3.0", "@supabase/supabase-js": "^2.39.0", "@octokit/rest": "^20.0.0", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^4.18.0", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "twilio": "^4.21.0", "ws": "^8.14.0", "@whatsite-bot/core": "workspace:*", "@whatsite-bot/workspace-manager": "workspace:*", "@whatsite-bot/gemini-runner": "workspace:*"}, "devDependencies": {"@types/node": "^20.10.0", "@types/express": "^4.17.0", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/ws": "^8.5.0", "typescript": "^5.3.0", "tsx": "^4.0.0", "vitest": "^1.0.0", "prettier": "^3.0.0", "eslint": "^8.0.0"}}