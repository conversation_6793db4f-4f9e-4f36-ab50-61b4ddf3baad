/**
 * Example usage of WebhookRouter with BotEngine
 *
 * This file demonstrates different ways to use the refactored webhook handling:
 * 1. Using WebhookServer (backward compatible)
 * 2. Using WebhookRouter directly (manual integration)
 */

import express, { json, static as expressStatic } from 'express';

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, BotEngineConfig } from '../src/core/bot-engine.js';
import { WebhookServer } from '../src/communication/webhook-server.js';
import { WebhookRouter } from '../src/communication/webhook-router.js';
import { WebhookHandler } from '../src/communication/webhook-handler.js';
import { StatusCommunicator } from '../src/communication/status-communicator.js';

// Example configuration
const config: BotEngineConfig = {
  google: {
    apiKey: 'your-gemini-api-key',
    model: 'gemini-pro',
    maxTokens: 2048,
  },
  github: {
    token: 'your-github-token',
    organization: 'your-org',
  },
  vercel: {
    token: 'your-vercel-token',
    teamId: 'your-team-id',
  },
  twilio: {
    accountSid: 'your-account-sid',
    authToken: 'your-auth-token',
    phoneNumber: '+**********',
    webhookUrl: 'https://your-domain.com/webhook',
  },
  server: {
    host: 'localhost',
    port: 3000,
    webhookPath: '/webhook',
    useExpress: true,
    expressConfig: {
      enableCors: true,
      enableHelmet: true,
      enableMorgan: true,
      enableRateLimit: true,
    },
  },
  workspace: {
    baseDir: './workspace',
    maxWorkspacesPerUser: 5,
    timeout: 1800000, // 30 minutes
  },
  features: {
    enableWebsiteCreation: true,
    enableWebsiteEditing: true,
    enableRepositoryImport: true,
    enableDeployment: true,
    enableGitIntegration: true,
    enableStatusUpdates: true,
    enableErrorRecovery: true,
  },
  performance: {
    enableOptimizations: true,
    enableCaching: true,
    enableConnectionPooling: true,
    enableMetrics: true,
    enableResourceManagement: true,
    cacheConfig: {
      maxSize: 1000,
      ttl: 300000,
      evictionPolicy: 'lru',
    },
    connectionPoolConfig: {
      maxConnections: 100,
      idleTimeout: 30000,
      requestTimeout: 30000,
    },
    metricsConfig: {
      enablePrometheus: true,
      metricsInterval: 30000,
    },
    resourceConfig: {
      maxMemoryUsage: 512 * 1024 * 1024, // 512MB
      gcThreshold: 100 * 1024 * 1024, // 100MB
      cleanupInterval: 300000,
    },
  },
};

// Webhook server configuration
const webhookServerConfig = {
  port: 3000,
  host: 'localhost',
  basePath: '/webhook',
  enableCors: true,
  enableLogging: true,
  enableRateLimit: true,
  rateLimit: {
    windowMs: 15 * 60 * 1000,
    max: 100,
    message: 'Too many requests from this IP, please try again later.',
  },
  auth: {
    requireSignature: false,
    enableRateLimit: true,
  },
  enableHealthCheck: true,
  enableMetrics: true,
  requestTimeout: 30000,
  bodySizeLimit: '10mb',
};

/**
 * Example 1: Using WebhookServer (backward compatible)
 * This is the existing way - no changes needed
 */
async function example1WebhookServer() {
  console.log('=== Example 1: Using WebhookServer ===');

  const botEngine = new BotEngine(config);
  const webhookHandler = new WebhookHandler(botEngine);
  const statusCommunicator = new StatusCommunicator();
  const webhookServer = new WebhookServer(
    botEngine,
    webhookHandler,
    statusCommunicator,
    webhookServerConfig
  );

  await webhookServer.start();
  console.log('WebhookServer started on port 3000');
}

/**
 * Example 2: Using WebhookRouter directly (manual integration)
 * This gives full control over Express setup
 */
async function example2WebhookRouter() {
  console.log('=== Example 2: Using WebhookRouter directly ===');

  const app = express();
  const botEngine = new BotEngine(config);
  const webhookHandler = new WebhookHandler(botEngine);
  const statusCommunicator = new StatusCommunicator();

  // Create WebhookRouter
  const webhookRouter = new WebhookRouter(webhookHandler, statusCommunicator, {
    basePath: '/api',
    enableCors: true,
    enableLogging: true,
    enableRateLimit: true,
    rateLimit: {
      windowMs: 15 * 60 * 1000,
      max: 100,
      message: 'Too many requests from this IP, please try again later.',
    },
    auth: {
      requireSignature: false,
      enableRateLimit: true,
    },
    enableHealthCheck: true,
    enableMetrics: true,
    requestTimeout: 30000,
    bodySizeLimit: '10mb',
  });

  // Mount webhook router
  app.use('/api/webhook', webhookRouter.getRouter());

  // Add custom routes
  app.get('/health', (req, res) => {
    res.json({ status: 'ok', timestamp: new Date().toISOString() });
  });

  // Start server
  return new Promise<void>(resolve => {
    app.listen(3000, () => {
      console.log('Express server with WebhookRouter started on port 3000');
      resolve();
    });
  });
}

/**
 * Example 3: Using WebhookRouter with existing Express app
 * This shows how to integrate with an existing Express application
 */
async function example3ExistingExpress() {
  console.log('=== Example 3: Using with existing Express app ===');

  const app = express();

  app.use(json());
  app.use('/static', expressStatic('public'));

  // Create BotEngine
  const botEngine = new BotEngine(config);
  const webhookHandler = new WebhookHandler(botEngine);
  const statusCommunicator = new StatusCommunicator();

  // Create WebhookRouter
  const webhookRouter = new WebhookRouter(webhookHandler, statusCommunicator, {
    basePath: '/webhook',
    enableCors: true,
    enableLogging: true,
    enableRateLimit: true,
    rateLimit: {
      windowMs: 15 * 60 * 1000,
      max: 50,
      message: 'Too many requests from this IP, please try again later.',
    },
    auth: {
      requireSignature: false,
      enableRateLimit: true,
    },
    enableHealthCheck: true,
    enableMetrics: true,
    requestTimeout: 30000,
    bodySizeLimit: '10mb',
  });

  // Mount webhook router at specific path
  app.use('/webhook', webhookRouter.getRouter());

  // Start server
  return new Promise<void>(resolve => {
    app.listen(3000, () => {
      console.log('Existing Express app with WebhookRouter started on port 3000');
      resolve();
    });
  });
}

// Run examples based on environment
const example = process.env.EXAMPLE || '1';

switch (example) {
  case '1':
    example1WebhookServer().catch(console.error);
    break;
  case '2':
    example2WebhookRouter().catch(console.error);
    break;
  case '3':
    example3ExistingExpress().catch(console.error);
    break;
  default:
    console.log('Please set EXAMPLE=1,2, or 3 to run specific example');
}
