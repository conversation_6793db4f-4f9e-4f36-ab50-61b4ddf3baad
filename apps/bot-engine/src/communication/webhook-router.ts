/**
 * Express router for webhook endpoints
 * Provides mountable router for webhook communication that can be used with any Express app
 */

import { randomUUID } from 'crypto';

import type { WebhookEvent } from '@whatsite-bot/core';
import { Router, Request, Response, NextFunction, json, urlencoded } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import { default as rateLimit } from 'express-rate-limit';

import {
  api<PERSON>eyAuth,
  signatureAuth,
  validateWebhookRequest,
  securityHeaders,
  requestLogger,
  AuthConfig,
} from '../middleware/auth.js';

import { WebhookHandler } from './webhook-handler.js';
import { StatusCommunicator } from './status-communicator.js';

/**
 * Webhook router configuration
 */
export interface WebhookRouterConfig {
  /** Base path for webhook endpoints */
  basePath: string;
  /** Enable CORS */
  enableCors: boolean;
  /** Enable request logging */
  enableLogging: boolean;
  /** Enable rate limiting */
  enableRateLimit: boolean;
  /** Rate limit configuration */
  rateLimit: {
    windowMs: number;
    max: number;
    message: string;
  };
  /** Authentication configuration */
  auth: AuthConfig;
  /** Request timeout in milliseconds */
  requestTimeout: number;
  /** Enable request body size limit */
  bodySizeLimit: string;
}

/**
 * Default webhook router configuration
 */
const DEFAULT_WEBHOOK_ROUTER_CONFIG: WebhookRouterConfig = {
  basePath: '/webhook',
  enableCors: true,
  enableLogging: true,
  enableRateLimit: true,
  rateLimit: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100,
    message: 'Too many requests from this IP, please try again later.',
  },
  auth: {
    apiKey: undefined,
    secretKey: undefined,
    requireSignature: false,
    enableRateLimit: true,
  },
  requestTimeout: 30000,
  bodySizeLimit: '10mb',
};

/**
 * Webhook router for handling webhook endpoints
 */
export class WebhookRouter {
  private router: Router;
  private webhookHandler: WebhookHandler;
  private statusCommunicator: StatusCommunicator;
  private config: WebhookRouterConfig;
  private requestCount = 0;
  private errorCount = 0;
  private startTime = Date.now();

  constructor(
    webhookHandler: WebhookHandler,
    statusCommunicator: StatusCommunicator,
    config?: Partial<WebhookRouterConfig>
  ) {
    this.webhookHandler = webhookHandler;
    this.statusCommunicator = statusCommunicator;
    this.config = { ...DEFAULT_WEBHOOK_ROUTER_CONFIG, ...config };
    this.router = Router();

    this.setupMiddleware();
    this.setupRoutes();
  }

  /**
   * Get the Express router instance
   */
  public getRouter(): Router {
    return this.router;
  }

  /**
   * Setup middleware
   */
  private setupMiddleware(): void {
    // Security middleware
    this.router.use(helmet());
    this.router.use(securityHeaders());

    // Request logging
    if (this.config.enableLogging) {
      this.router.use(morgan('combined'));
    }

    // Request logging middleware
    this.router.use(requestLogger());

    // Rate limiting
    if (this.config.enableRateLimit) {
      const limiter = rateLimit({
        windowMs: this.config.rateLimit.windowMs,
        max: this.config.rateLimit.max,
        message: this.config.rateLimit.message,
        standardHeaders: true,
        legacyHeaders: false,
      });
      this.router.use(limiter);
    }

    // CORS
    if (this.config.enableCors) {
      this.router.use(cors());
    }

    // Body parsing
    this.router.use(json({ limit: this.config.bodySizeLimit }));
    this.router.use(urlencoded({ extended: true, limit: this.config.bodySizeLimit }));

    // Authentication
    if (this.config.auth.apiKey) {
      this.router.use(apiKeyAuth(this.config.auth));
    }
    if (this.config.auth.secretKey) {
      this.router.use(signatureAuth(this.config.auth));
    }

    // Request timeout
    this.router.use((req: Request, res: Response, next: NextFunction) => {
      const timeout = setTimeout(() => {
        res.status(408).json({
          error: 'Request timeout',
          message: 'Request took too long to process',
        });
      }, this.config.requestTimeout);

      res.on('finish', () => clearTimeout(timeout));
      res.on('close', () => clearTimeout(timeout));
      next();
    });
  }

  /**
   * Setup routes
   */
  private setupRoutes(): void {
    // Main webhook endpoint - health checks are handled at root level by BotEngine
    this.router.post(
      '/',
      validateWebhookRequest(),
      (req: Request, res: Response, next: NextFunction) => {
        this.handleWebhook(req, res).catch(next);
      }
    );

    // Status update endpoint
    this.router.post('/status', (req: Request, res: Response, next: NextFunction) => {
      this.handleStatusUpdate(req, res).catch(next);
    });

    // Test endpoint
    this.router.get('/test', this.handleTestWebhook.bind(this));

    // General error handler
    this.router.use((error: Error, req: Request, res: Response, _next: NextFunction) => {
      console.error('[WebhookRouter] Error:', error);
      this.errorCount++;

      res.status(500).json({
        error: 'Internal server error',
        message: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong',
        requestId: (req as { id?: string }).id || 'unknown',
      });
    });
  }

  /**
   * Handle webhook requests
   */
  private async handleWebhook(req: Request, res: Response): Promise<void> {
    const requestId = randomUUID();
    const startTime = Date.now();

    try {
      // Add request ID to request object
      (req as { id?: string }).id = requestId;

      console.log(`[WebhookRouter] Processing webhook: ${requestId}`);

      // Validate webhook event
      const event = req.body as WebhookEvent;
      if (!event || typeof event !== 'object') {
        res.status(400).json({
          error: 'Invalid webhook event',
          message: 'Request body must be a valid webhook event',
          requestId,
        });
        return;
      }

      // Process webhook event using the core interface method
      const result = await this.webhookHandler.handle(event);

      // Send status update
      await this.statusCommunicator.sendStatusUpdate(
        requestId,
        'system',
        `Webhook processed successfully: ${event.type || 'unknown'}`,
        'success',
        {
          eventType: event.type || 'unknown',
          processingTime: Date.now() - startTime,
          result,
        }
      );

      // Send response
      res.json({
        success: true,
        message: 'Webhook processed successfully',
        requestId,
        result,
      });
    } catch (error) {
      console.error(`[WebhookRouter] Error processing webhook: ${requestId}`, error);

      // Send error status update
      await this.statusCommunicator.sendStatusUpdate(
        requestId,
        'system',
        `Webhook processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'error',
        {
          error: error instanceof Error ? error.message : 'Unknown error',
          stack: error instanceof Error ? error.stack : undefined,
        }
      );

      res.status(500).json({
        error: 'Failed to process webhook',
        message: error instanceof Error ? error.message : 'Unknown error',
        requestId,
      });
    }
  }

  // Health check methods removed - handled at root level by BotEngine

  /**
   * Handle status updates
   */
  private async handleStatusUpdate(req: Request, res: Response): Promise<void> {
    const requestId = randomUUID();

    try {
      const {
        requestId: originalRequestId,
        status,
        message,
        data,
      } = req.body as {
        requestId: string;
        status: string;
        message?: string;
        data?: Record<string, unknown>;
      };

      if (!originalRequestId || !status) {
        res.status(400).json({
          error: 'Invalid status update',
          message: 'Missing required fields: requestId, status',
          requestId,
        });
        return;
      }

      console.log(`[WebhookRouter] Processing status update: ${originalRequestId} - ${status}`);

      // Process the status update
      await this.statusCommunicator.sendStatusUpdate(
        originalRequestId,
        'system',
        message || 'Status update received',
        'info',
        data
      );

      res.json({
        success: true,
        message: 'Status update processed',
        requestId,
      });
    } catch (error) {
      console.error(`[WebhookRouter] Error processing status update: ${requestId}`, error);

      res.status(500).json({
        error: 'Failed to process status update',
        message: error instanceof Error ? error.message : 'Unknown error',
        requestId,
      });
    }
  }

  /**
   * Handle test webhook
   */
  private handleTestWebhook(req: Request, res: Response): void {
    const requestId = randomUUID();

    console.log(`[WebhookRouter] Processing test webhook: ${requestId}`);

    res.json({
      success: true,
      message: 'Test webhook processed successfully',
      requestId,
    });
  }
}

/**
 * Create webhook router instance
 */
export function createWebhookRouter(
  webhookHandler: WebhookHandler,
  statusCommunicator: StatusCommunicator,
  config?: Partial<WebhookRouterConfig>
): WebhookRouter {
  return new WebhookRouter(webhookHandler, statusCommunicator, config);
}
