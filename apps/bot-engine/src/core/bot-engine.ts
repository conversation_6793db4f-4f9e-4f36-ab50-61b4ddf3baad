import { EventEmitter } from 'events';

import express, { json, urlencoded } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import { rateLimit, Options as RateLimitOptions } from 'express-rate-limit';
import {
  WebhookEvent,
  WebhookResponse,
  WebhookEventType,
  MessageReceivedEvent,
  SessionStartedEvent,
  SessionCompletedEvent,
  SessionFailedEvent,
  SystemErrorEvent,
  User,
  WebhookWhatsAppMessage,
  IntentClassification,
} from '@whatsite-bot/core';

import { WebsiteSession, SessionManager } from '../session/session-manager.js';
import { WebhookRouter } from '../communication/webhook-router.js';
import { WebsiteManager } from '../website/website-manager.js';
import { StatusMessenger } from '../communication/status-messenger.js';
import { WebhookHandler } from '../communication/webhook-handler.js';
import { StatusCommunicator } from '../communication/status-communicator.js';

// Configuration interface
export interface BotEngineConfig {
  twilio: {
    accountSid: string;
    authToken: string;
    phoneNumber: string;
    webhookUrl: string;
  };
  google: {
    apiKey: string;
    model: string;
    maxTokens: number;
  };
  github: {
    token: string;
    organization?: string;
  };
  vercel: {
    token: string;
    teamId?: string;
  };
  server: {
    port: number;
    host: string;
    expressConfig?: {
      corsOptions?: cors.CorsOptions;
      rateLimitOptions?: Partial<RateLimitOptions>;
    };
  };
  workspace: {
    baseDir: string;
    maxWorkspacesPerUser: number;
    timeout: number;
  };
  features: {
    enableWebsiteCreation: boolean;
    enableWebsiteEditing: boolean;
    enableRepositoryImport: boolean;
    enableDeployment: boolean;
    enableGitIntegration: boolean;
    enableStatusUpdates: boolean;
    enableErrorRecovery: boolean;
  };
  performance: {
    enableOptimizations: boolean;
    enableCaching: boolean;
    enableConnectionPooling: boolean;
    enableMetrics: boolean;
    enableResourceManagement: boolean;
    cacheConfig: {
      maxSize: number;
      ttl: number;
      evictionPolicy: 'lru' | 'lfu' | 'fifo';
    };
    connectionPoolConfig: {
      maxConnections: number;
      idleTimeout: number;
      requestTimeout: number;
    };
    metricsConfig: {
      enablePrometheus: boolean;
      metricsInterval: number;
    };
    resourceConfig: {
      maxMemoryUsage: number;
      gcThreshold: number;
      cleanupInterval: number;
    };
  };
  webhook: {
    path: string;
    secret?: string;
  };
}

// Session tracking
interface SessionTracking {
  sessionId: string;
  startTime: Date;
  lastActivity: Date;
  operation: string;
}

/**
 * Main Bot Engine class that handles WhatsApp webhook events and manages user sessions
 */
export class BotEngine extends EventEmitter {
  private app: express.Application | null = null;
  private server: ReturnType<express.Application['listen']> | null = null;
  public isRunning = false;
  private isShuttingDown = false;
  private userSessions: Map<string, Set<string>> = new Map();
  private activeSessions: Map<string, SessionTracking> = new Map();

  // Managers
  private sessionManager: SessionManager;
  private websiteManager: WebsiteManager;
  private statusMessenger: StatusMessenger;
  private webhookRouter: WebhookRouter;
  private webhookHandler: WebhookHandler;
  private statusCommunicator: StatusCommunicator;

  constructor(
    private config: BotEngineConfig,
    sessionManager: SessionManager,
    websiteManager: WebsiteManager,
    statusMessenger: StatusMessenger
  ) {
    super(); // Call EventEmitter constructor
    this.sessionManager = sessionManager;
    this.websiteManager = websiteManager;
    this.statusMessenger = statusMessenger;

    // Initialize StatusCommunicator
    this.statusCommunicator = new StatusCommunicator();

    // Initialize WebhookHandler with BotEngine instance
    this.webhookHandler = new WebhookHandler(this);

    // Initialize WebhookRouter with WebhookHandler and StatusCommunicator
    this.webhookRouter = new WebhookRouter(this.webhookHandler, this.statusCommunicator, {
      basePath: this.config.webhook.path,
      auth: { secretKey: this.config.webhook.secret },
    });
  }

  /**
   * Get supported webhook event types (Delegated to WebhookHandler)
   */
  getSupportedEvents(): WebhookEventType[] {
    return this.webhookHandler.getSupportedEvents();
  }

  /**
   * Handle webhook event (Delegated to WebhookHandler)
   */
  async handle(event: WebhookEvent): Promise<WebhookResponse> {
    try {
      console.log(`[BotEngine] Handling event: ${event.type}`);

      switch (event.type) {
        case 'message.received':
          return await this.handleMessageReceived(event);
        case 'session.started':
          return this.handleSessionStarted(event);
        case 'session.completed':
          return await this.handleSessionCompleted(event);
        case 'session.failed':
          return await this.handleSessionFailed(event);
        case 'system.error':
          return await this.handleSystemError(event);
        default:
          return {
            status: 'success',
            message: `Event type ${event.type} not handled by BotEngine`,
          };
      }
    } catch (error) {
      console.error('[BotEngine] Error handling event:', error);
      return {
        status: 'error',
        message: `Failed to handle event: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  /**
   * Handle message received event
   */
  private async handleMessageReceived(event: MessageReceivedEvent): Promise<WebhookResponse> {
    try {
      const { from, message } = event.data;

      console.log(
        `[BotEngine] Processing message from ${from.phoneNumber}: ${this.getMessageText(message)}`
      );

      // Classify intent
      const intent = this.classifyIntent(this.getMessageText(message));

      // Get or create session
      const sessionId = await this.getOrCreateSession(from, intent);

      // Process intent
      await this.processIntent(sessionId, from, intent, message);

      return {
        status: 'success',
        message: 'Message processed successfully',
        data: { sessionId },
      };
    } catch (error) {
      console.error('[BotEngine] Error processing message:', error);
      return {
        status: 'error',
        message: `Failed to process message: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  /**
   * Handle session started event
   */
  private handleSessionStarted(event: SessionStartedEvent): WebhookResponse {
    const { session } = event.data;

    console.log(`[BotEngine] Session started: ${session.id} for ${session.user.phoneNumber}`);

    // Track session
    this.trackSession(session.id, session.user.phoneNumber, session.operation);

    return {
      status: 'success',
      message: 'Session started successfully',
      data: { sessionId: session.id },
    };
  }

  /**
   * Handle session completed event
   */
  private async handleSessionCompleted(event: SessionCompletedEvent): Promise<WebhookResponse> {
    try {
      const { session, completion } = event.data;

      console.log(`[BotEngine] Session completed: ${session.id} for ${session.user.phoneNumber}`);

      // Send completion message
      await this.statusMessenger.sendOperationSuccess(session.user, 'session_completed', {
        message: completion.message,
        files: completion.files,
        deploymentUrl: completion.deploymentUrl,
      });

      // Cleanup session
      await this.cleanupSession(session.id);

      return {
        status: 'success',
        message: 'Session completed successfully',
        data: { sessionId: session.id },
      };
    } catch (error) {
      console.error('[BotEngine] Error handling session completion:', error);
      return {
        status: 'error',
        message: `Failed to handle session completion: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  /**
   * Handle session failed event
   */
  private async handleSessionFailed(event: SessionFailedEvent): Promise<WebhookResponse> {
    try {
      const { session, error } = event.data;

      console.error(
        `[BotEngine] Session failed: ${session.id} for ${session.user.phoneNumber}`,
        error
      );

      // Send error message
      await this.statusMessenger.sendOperationError(
        session.user,
        'session_failed',
        new Error(error.message)
      );

      // Cleanup session
      await this.cleanupSession(session.id);

      return {
        status: 'error',
        message: `Session failed: ${error.message}`,
        data: { sessionId: session.id },
      };
    } catch (err) {
      console.error('[BotEngine] Error handling session failure:', err);
      return {
        status: 'error',
        message: `Failed to handle session failure: ${err instanceof Error ? err.message : String(err)}`,
      };
    }
  }

  /**
   * Handle system error event
   */
  private async handleSystemError(event: SystemErrorEvent): Promise<WebhookResponse> {
    try {
      const { error, context } = event.data;

      console.error('[BotEngine] System error:', error);

      // Send error to affected user if context available
      if (context?.user) {
        await this.statusMessenger.sendOperationError(
          context.user,
          'system_error',
          new Error(error.message)
        );
      }

      return {
        status: 'error',
        message: `System error: ${error.message}`,
      };
    } catch (err) {
      console.error('[BotEngine] Error handling system error:', err);
      return {
        status: 'error',
        message: `Failed to handle system error: ${err instanceof Error ? err.message : String(err)}`,
      };
    }
  }

  /**
   * Get message text from WhatsApp message
   */
  private getMessageText(message: WebhookWhatsAppMessage): string {
    if (message.type === 'text' && message.content?.type === 'text' && message.content?.text) {
      return message.content.text;
    }
    return '';
  }

  /**
   * Classify user intent from message text
   */
  private classifyIntent(messageText: string): IntentClassification {
    const text = messageText.toLowerCase().trim();

    // Simple keyword-based classification
    if (text.includes('create') || text.includes('new') || text.includes('build')) {
      return {
        intent: 'new_site',
        confidence: 0.9,
        reasoning: 'User wants to create a new website',
      };
    }

    if (text.includes('edit') || text.includes('update') || text.includes('modify')) {
      return {
        intent: 'edit_site',
        confidence: 0.9,
        reasoning: 'User wants to edit an existing website',
      };
    }

    if (text.includes('import') || text.includes('repository') || text.includes('repo')) {
      return {
        intent: 'import_repo',
        confidence: 0.9,
        reasoning: 'User wants to import from a repository',
      };
    }

    if (text.includes('scaffold') || text.includes('template') || text.includes('boilerplate')) {
      return {
        intent: 'scaffold_mode',
        confidence: 0.9,
        reasoning: 'User wants to use scaffold mode',
      };
    }

    return {
      intent: 'new_site',
      confidence: 0.5,
      reasoning: 'Default to new site creation',
    };
  }

  /**
   * Get or create session for user
   */
  private async getOrCreateSession(user: User, intent: IntentClassification): Promise<string> {
    const existingSessions = this.userSessions.get(user.phoneNumber);

    if (existingSessions && existingSessions.size > 0) {
      // Return most recent active session
      const sessionIds = Array.from(existingSessions);
      return sessionIds[sessionIds.length - 1];
    }

    // Create new session
    const session = await this.sessionManager.createSession({
      user,
      operation: intent.intent,
      metadata: {
        intent,
        startTime: new Date(),
      },
      // Dummy workspace for now, will be properly initialized by WebsiteManager
      workspace: {
        id: 'temp',
        path: '',
        isGitWorktree: false,
        type: 'temporary',
        user: user,
        createdAt: new Date(),
        lastAccessedAt: new Date(),
        isActive: true,
      },
    });

    // Track session
    this.trackSession(session.id, user.phoneNumber, intent.intent);

    return session.id;
  }

  /**
   * Track session for user
   */
  private trackSession(sessionId: string, phoneNumber: string, operation: string): void {
    if (!this.userSessions.has(phoneNumber)) {
      this.userSessions.set(phoneNumber, new Set());
    }

    this.userSessions.get(phoneNumber)!.add(sessionId);

    this.activeSessions.set(sessionId, {
      sessionId,
      startTime: new Date(),
      lastActivity: new Date(),
      operation,
    });
  }

  /**
   * Process user intent
   */
  private async processIntent(
    sessionId: string,
    user: User,
    intent: IntentClassification,
    message: WebhookWhatsAppMessage
  ): Promise<void> {
    try {
      const messageText = this.getMessageText(message);
      const session = await this.sessionManager.getSession(sessionId);

      if (!session) {
        console.error(`[BotEngine] Session ${sessionId} not found.`);
        await this.statusMessenger.sendOperationError(
          user,
          'session_not_found',
          new Error('Your session could not be found. Please start a new interaction.')
        );
        return;
      }

      switch (intent.intent) {
        case 'new_site':
          await this.handleCreateWebsite(session, messageText);
          break;
        case 'edit_site':
          await this.handleEditWebsite(session, messageText);
          break;
        case 'import_repo':
          await this.handleImportRepository(session, messageText);
          break;
        case 'scaffold_mode':
          await this.handleScaffoldMode(session, messageText);
          break;
        default:
          await this.handleUnknownIntent(session);
      }
    } catch (error) {
      console.error('[BotEngine] Error processing intent:', error);
      await this.statusMessenger.sendOperationError(
        user,
        'processing_intent_error',
        new Error('Error processing your request')
      );
    }
  }

  /**
   * Handle create website intent
   */
  private async handleCreateWebsite(session: WebsiteSession, messageText: string): Promise<void> {
    await this.statusMessenger.sendOperationStart(session.user, 'create_website_start', {
      description: messageText,
    });

    try {
      const projectData = await this.websiteManager.createWebsite(session);

      await this.statusMessenger.sendOperationSuccess(session.user, 'create_website_success', {
        url: projectData.url,
        projectName: projectData.metadata?.projectName,
      });
    } catch (error) {
      console.error('[BotEngine] Error creating website:', error);
      await this.statusMessenger.sendOperationError(
        session.user,
        'create_website_failed',
        new Error('Failed to create website')
      );
    }
  }

  /**
   * Handle edit website intent
   */
  private async handleEditWebsite(session: WebsiteSession, messageText: string): Promise<void> {
    await this.statusMessenger.sendOperationStart(session.user, 'edit_website_start', {
      description: messageText,
    });

    try {
      const projectData = await this.websiteManager.editWebsite(session);

      await this.statusMessenger.sendOperationSuccess(session.user, 'edit_website_success', {
        url: projectData.url,
        projectName: projectData.metadata?.projectName,
      });
    } catch (error) {
      console.error('[BotEngine] Error editing website:', error);
      await this.statusMessenger.sendOperationError(
        session.user,
        'edit_website_failed',
        new Error('Failed to edit website')
      );
    }
  }

  /**
   * Handle import repository intent
   */
  private async handleImportRepository(
    session: WebsiteSession,
    messageText: string
  ): Promise<void> {
    await this.statusMessenger.sendOperationStart(session.user, 'import_repository_start', {
      description: messageText,
    });

    try {
      const repositoryUrl = this.extractRepositoryUrl(messageText);

      if (!repositoryUrl) {
        await this.statusMessenger.sendOperationError(
          session.user,
          'import_repository_failed',
          new Error('Please provide a valid repository URL')
        );
        return;
      }

      const projectData = await this.websiteManager.importRepository({
        type: 'url',
        fullUrl: repositoryUrl,
      });

      if (projectData.success) {
        await this.statusMessenger.sendOperationSuccess(session.user, 'import_repository_success', {
          url: projectData.deploymentUrl,
          repository: projectData.repositoryUrl,
        });
      } else {
        await this.statusMessenger.sendOperationError(
          session.user,
          'import_repository_failed',
          new Error(projectData.error || 'Failed to import repository')
        );
      }
    } catch (error) {
      console.error('[BotEngine] Error importing repository:', error);
      await this.statusMessenger.sendOperationError(
        session.user,
        'import_repository_failed',
        new Error('Failed to import repository')
      );
    }
  }

  /**
   * Handle scaffold mode intent
   */
  private async handleScaffoldMode(session: WebsiteSession, messageText: string): Promise<void> {
    await this.statusMessenger.sendOperationStart(session.user, 'scaffold_mode_start', {
      description: messageText,
    });

    try {
      const projectData = await this.websiteManager.createWebsite(session);

      await this.statusMessenger.sendOperationSuccess(session.user, 'scaffold_mode_success', {
        url: projectData.url,
        projectName: projectData.metadata?.projectName,
      });
    } catch (error) {
      console.error('[BotEngine] Error creating scaffold:', error);
      await this.statusMessenger.sendOperationError(
        session.user,
        'scaffold_mode_failed',
        new Error('Failed to create scaffold')
      );
    }
  }

  /**
   * Handle unknown intent
   */
  private async handleUnknownIntent(session: WebsiteSession): Promise<void> {
    await this.statusMessenger.sendInfo(session.user, 'unknown_intent', {
      message: 'I can help you create, edit, or import websites. What would you like to do?',
    });
  }

  /**
   * Extract repository URL from message text
   */
  private extractRepositoryUrl(messageText: string): string | null {
    const urlRegex = /(https?:\/\/github\.com\/[^\s]+)/i;
    const match = messageText.match(urlRegex);
    return match ? match[1] : null;
  }

  /**
   * Cleanup session
   */
  private async cleanupSession(sessionId: string): Promise<void> {
    // Remove from active sessions
    this.activeSessions.delete(sessionId);

    // Remove from user sessions
    for (const [phoneNumber, sessions] of this.userSessions.entries()) {
      sessions.delete(sessionId);
      if (sessions.size === 0) {
        this.userSessions.delete(phoneNumber);
      }
    }
    await this.sessionManager.deleteSession(sessionId);
  }

  /**
   * Start the BotEngine server
   */
  public start(): Promise<void> {
    if (this.isRunning) {
      console.warn('[BotEngine] Server already running.');
      return Promise.resolve();
    }

    this.app = express();
    this.app.use(json());
    this.app.use(urlencoded({ extended: true }));

    // Apply security middleware
    this.app.use(helmet());

    // Apply CORS
    this.app.use(cors(this.config.server.expressConfig?.corsOptions));

    // Logging
    this.app.use(morgan('combined'));

    // Rate limiting
    if (this.config.server.expressConfig?.rateLimitOptions) {
      this.app.use(rateLimit(this.config.server.expressConfig.rateLimitOptions));
    }

    // Register webhook router
    this.app.use(this.config.webhook.path, this.webhookRouter.getRouter());

    // Health check endpoints for Fly.io
    this.setupHealthCheckEndpoints();

    return new Promise(resolve => {
      this.server = this.app!.listen(this.config.server.port, this.config.server.host, () => {
        this.isRunning = true;
        console.log(
          `[BotEngine] Server listening on http://${this.config.server.host}:${this.config.server.port}`
        );
        // Handle graceful shutdown
        process.on('SIGTERM', () => void this.gracefulShutdown());
        process.on('SIGINT', () => void this.gracefulShutdown());
        resolve();
      });
    });
  }

  /**
   * Setup health check endpoints for Fly.io
   */
  private setupHealthCheckEndpoints(): void {
    if (!this.app) {
      return;
    }

    // Health check endpoint - basic liveness check
    this.app.get('/health', (_req, res) => {
      res.status(200).json({
        service: 'BotEngine',
        status: 'healthy',
        timestamp: new Date().toISOString(),
        version: '1.0.0',
      });
    });

    // Readiness check - checks if all services are ready
    this.app.get('/ready', (_req, res) => {
      const isReady = this.isRunning && this.webhookRouter;
      res.status(isReady ? 200 : 503).json({
        service: 'BotEngine',
        status: isReady ? 'ready' : 'not_ready',
        timestamp: new Date().toISOString(),
        checks: {
          server_running: this.isRunning,
          webhook_router: !!this.webhookRouter,
        },
      });
    });

    // Metrics endpoint - basic metrics for monitoring
    this.app.get('/metrics', (req, res) => {
      const acceptHeader = req.headers.accept || '';
      const uptime = process.uptime();
      const memoryUsage = process.memoryUsage();

      if (acceptHeader.includes('text/plain')) {
        // Prometheus format
        const prometheusMetrics = [
          `# HELP bot_engine_uptime_seconds Uptime in seconds`,
          `# TYPE bot_engine_uptime_seconds gauge`,
          `bot_engine_uptime_seconds ${Math.floor(uptime)}`,
          `# HELP bot_engine_memory_usage_bytes Memory usage in bytes`,
          `# TYPE bot_engine_memory_usage_bytes gauge`,
          `bot_engine_memory_usage_bytes{type="rss"} ${memoryUsage.rss}`,
          `bot_engine_memory_usage_bytes{type="heapTotal"} ${memoryUsage.heapTotal}`,
          `bot_engine_memory_usage_bytes{type="heapUsed"} ${memoryUsage.heapUsed}`,
          `bot_engine_memory_usage_bytes{type="external"} ${memoryUsage.external}`,
        ].join('\n');

        res.set('Content-Type', 'text/plain');
        res.send(prometheusMetrics);
      } else {
        // JSON format
        res.json({
          service: 'BotEngine',
          status: 'ok',
          timestamp: new Date().toISOString(),
          uptime,
          memory: memoryUsage,
          server_running: this.isRunning,
        });
      }
    });
  }

  /**
   * Get the HTTP server instance
   */
  getServer(): ReturnType<express.Application['listen']> | null {
    return this.server;
  }

  /**
   * Stop the BotEngine server
   */
  public stop(): Promise<void> {
    if (!this.server || !this.isRunning) {
      console.warn('[BotEngine] Server is not running.');
      return Promise.resolve();
    }

    return new Promise<void>((resolve, reject) => {
      this.server!.close((err: Error | undefined) => {
        if (err) {
          console.error('[BotEngine] Error closing server:', err);
          return reject(err);
        }
        console.log('[BotEngine] Server stopped.');
        this.app = null;
        this.server = null;
        this.isRunning = false;
        resolve();
      });
    });
  }

  /**
   * Graceful shutdown handler
   */
  private gracefulShutdown = async (): Promise<void> => {
    if (this.isShuttingDown) {
      return;
    }
    this.isShuttingDown = true;
    console.log('[BotEngine] Initiating graceful shutdown...');

    try {
      // Stop the server from accepting new connections
      await this.stop();

      // Perform cleanup (e.g., save active sessions, close DB connections)
      console.log('[BotEngine] Performing cleanup...');

      console.log('[BotEngine] Shutdown complete. Exiting.');
      process.exit(0);
    } catch (error) {
      console.error('[BotEngine] Error during graceful shutdown:', error);
      process.exit(1);
    }
  };

  public getStatus(): { isRunning: boolean } {
    return { isRunning: this.isRunning };
  }
}

export function createBotEngine(
  config: BotEngineConfig,
  sessionManager: SessionManager,
  websiteManager: WebsiteManager,
  statusMessenger: StatusMessenger
): BotEngine {
  return new BotEngine(config, sessionManager, websiteManager, statusMessenger);
}

export function getBotEngineStatus(botEngine: BotEngine): { isRunning: boolean } {
  return { isRunning: botEngine.isRunning };
}
