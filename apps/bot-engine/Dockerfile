# Multi-stage build for production optimization
FROM node:18-alpine AS dependencies

# Install system dependencies for native modules and git operations
RUN apk add --no-cache \
    git \
    python3 \
    make \
    g++ \
    libc6-compat \
    && ln -sf python3 /usr/bin/python

# Install pnpm globally
RUN npm install -g pnpm@9.0.0

# Set working directory
WORKDIR /app

# Copy package manager files
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./

# Copy package.json files for all workspace packages
COPY apps/bot-engine/package.json apps/bot-engine/
COPY packages/core/package.json packages/core/
COPY packages/workspace-manager/package.json packages/workspace-manager/
COPY packages/gemini-runner/package.json packages/gemini-runner/

# Install dependencies (including dev dependencies for build)
RUN pnpm install --frozen-lockfile

# ================================
# Build stage
# ================================
FROM dependencies AS builder

# Copy source code
COPY . .

# Build all packages in dependency order
RUN pnpm build:bot-engine

# ================================
# Production stage
# ================================
FROM node:18-alpine AS production

# Install system dependencies for runtime
RUN apk add --no-cache \
    git \
    curl \
    && addgroup -g 1001 -S nodejs \
    && adduser -S botengine -u 1001

# Install pnpm globally
RUN npm install -g pnpm@9.0.0

# Set working directory
WORKDIR /app

# Copy package manager files
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./

# Copy package.json files for all workspace packages
COPY apps/bot-engine/package.json apps/bot-engine/
COPY packages/core/package.json packages/core/
COPY packages/workspace-manager/package.json packages/workspace-manager/
COPY packages/gemini-runner/package.json packages/gemini-runner/

# Install production dependencies only
RUN pnpm install --frozen-lockfile --prod

# Copy built application from builder stage
COPY --from=builder /app/apps/bot-engine/dist ./apps/bot-engine/dist
COPY --from=builder /app/packages/core/dist ./packages/core/dist
COPY --from=builder /app/packages/workspace-manager/dist ./packages/workspace-manager/dist
COPY --from=builder /app/packages/gemini-runner/dist ./packages/gemini-runner/dist

# Create directories for workspace and session data
RUN mkdir -p /data/workspaces /data/sessions \
    && ln -s /data/workspaces  /app/workspaces \
    && ln -s /data/sessions    /app/sessions \
    && chown -R botengine:nodejs /data

# Set environment variables
ENV NODE_ENV=production \
    PORT=3000 \
    WORKSPACE_BASE_DIR=/data/workspaces \
    SESSION_STORAGE_DIR=/data/sessions

# Switch to non-root user
USER botengine

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/health || exit 1

# Start the application
CMD ["pnpm", "--filter", "bot-engine", "start"]