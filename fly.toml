# fly.toml app configuration file generated for whatsite-bot-engine on 2025-07-17T22:40:48+02:00
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = 'whatsite-bot-engine'
primary_region = 'sjc'
kill_signal = 'SIGINT'
kill_timeout = '10s'

[build]
  dockerfile = 'apps/bot-engine/Dockerfile'
  ignorefile = '.dockerignore'

[deploy]
  strategy = 'rolling'
  release_command = "echo 'Starting production deployment...'"

[env]
  NODE_ENV = 'production'
  PORT = '3000'
  LOG_LEVEL = 'info'
  METRICS_ENABLED = 'true'
  HEALTH_CHECK_TIMEOUT = '5s'
  GRACEFUL_SHUTDOWN_TIMEOUT = '30s'
  MAX_CONCURRENT_SESSIONS = '10'
  WORKSPACE_BASE_DIR = '/data/workspaces'
  SESSION_STORAGE_DIR = '/data/sessions'

[[mounts]]
  source = "whatsite_data"
  destination = "/data"

[http_service]
  internal_port = 3000
  force_https = true
  auto_start_machines = true
  auto_stop_machines = true
  min_machines_running = 0
  processes = ['app']

  [http_service.concurrency]
    type = 'connections'
    soft_limit = 800
    hard_limit = 1000

  # Liveness
  [[http_service.checks]]
    interval = '10s'
    timeout = '5s'
    grace_period = '10s'
    method = 'GET'
    path = '/health'
    protocol = 'http'
    restart_limit = 3
    [http_service.checks.headers]
      X-Forwarded-Proto = 'https'
      User-Agent = 'Fly Health Check'

  # Readiness
  [[http_service.checks]]
    interval = '30s'
    timeout = '10s'
    grace_period = '15s'
    method = 'GET'
    path = '/ready'
    protocol = 'http'
    restart_limit = 2
    [http_service.checks.headers]
      X-Forwarded-Proto = 'https'

  # Optional deep/metrics check
  [[http_service.checks]]
    interval = '60s'
    timeout = '15s'
    grace_period = '20s'
    method = 'GET'
    path = '/metrics'
    protocol = 'http'
    restart_limit = 1

# VM sizing (Machines)
[[vm]]
  cpu_kind = 'shared'
  cpus = 1
  memory = '1gb'